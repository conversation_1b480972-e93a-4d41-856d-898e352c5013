import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Card,
  Button,
  message,
  Space,
  Modal,
  Input,
  Tooltip,
  Tag,
  Progress,
  Spin,
  Upload
} from 'antd';
import {
  EditOutlined,
  ReloadOutlined,
  DeleteOutlined,
  PlusOutlined,
  SaveOutlined,
  DownloadOutlined,
  UploadOutlined,
  UndoOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import { 
  useReactTable, 
  getCoreRowModel, 
  getSortedRowModel, 
  getFilteredRowModel, 
  flexRender, 
  createColumnHelper 
} from '@tanstack/react-table';
import * as XLSX from 'xlsx';
import http from 'libs/http';
import styles from './ModelDataTableTanStack.module.less';

const { Search } = Input;

function ModelDataTableTanStack() {
  const { modelName } = useParams();
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [globalFilter, setGlobalFilter] = useState('');
  const [sorting, setSorting] = useState([]);
  const [editingCell, setEditingCell] = useState(null); // { rowId, columnId }
  const [editingValue, setEditingValue] = useState('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // 获取数据
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await http.get('/api/model-storage/performance-test-data/', {
        params: { model_name: modelName }
      });

      const transformedData = response.map((item, index) => ({
        id: item.id || `temp_${Date.now()}_${index}`,
        filename: item.filename || '',
        success_requests: item.success_requests,
        benchmark_duration: item.benchmark_duration,
        input_tokens: item.input_tokens,
        output_tokens: item.output_tokens,
        request_throughput: item.request_throughput,
        output_token_throughput: item.output_token_throughput,
        total_token_throughput: item.total_token_throughput,
        avg_ttft: item.avg_ttft,
        median_ttft: item.median_ttft,
        p99_ttft: item.p99_ttft,
        avg_tpot: item.avg_tpot,
        median_tpot: item.median_tpot,
        p99_tpot: item.p99_tpot,
        _modified: false,
        _isNew: !item.id
      }));

      setData(transformedData);
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('获取数据失败:', error);
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  }, [modelName]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 更新单元格数据
  const updateCell = useCallback((rowId, columnId, value) => {
    setData(prevData => 
      prevData.map(row => {
        if (row.id === rowId) {
          const column = columns.find(col => col.id === columnId);
          let processedValue = value;
          
          // 数据类型转换
          if (column && column.meta?.type === 'number') {
            if (value === '' || value === null || value === undefined) {
              processedValue = null;
            } else {
              const numValue = parseFloat(value);
              processedValue = isNaN(numValue) ? null : numValue;
            }
          }
          
          return { 
            ...row, 
            [columnId]: processedValue,
            _modified: true 
          };
        }
        return row;
      })
    );
    setHasUnsavedChanges(true);
  }, []);

  // 开始编辑单元格
  const startEditing = useCallback((rowId, columnId, currentValue) => {
    setEditingCell({ rowId, columnId });
    setEditingValue(currentValue || '');
  }, []);

  // 完成编辑
  const finishEditing = useCallback(() => {
    if (editingCell) {
      updateCell(editingCell.rowId, editingCell.columnId, editingValue);
      setEditingCell(null);
      setEditingValue('');
    }
  }, [editingCell, editingValue, updateCell]);

  // 取消编辑
  const cancelEditing = useCallback(() => {
    setEditingCell(null);
    setEditingValue('');
  }, []);

  // 可编辑单元格组件
  const EditableCell = ({ getValue, row, column, table }) => {
    const initialValue = getValue();
    const isEditing = editingCell?.rowId === row.original.id && editingCell?.columnId === column.id;
    
    if (isEditing) {
      return (
        <Input
          value={editingValue}
          onChange={(e) => setEditingValue(e.target.value)}
          onPressEnter={finishEditing}
          onBlur={finishEditing}
          onKeyDown={(e) => {
            if (e.key === 'Escape') {
              cancelEditing();
            }
          }}
          autoFocus
          size="small"
          style={{ width: '100%' }}
        />
      );
    }

    return (
      <div
        className={styles.editableCell}
        onClick={() => startEditing(row.original.id, column.id, initialValue)}
        title="点击编辑"
      >
        {initialValue || '-'}
      </div>
    );
  };

  // 表格列定义
  const columnHelper = createColumnHelper();
  const columns = useMemo(() => [
    columnHelper.accessor('filename', {
      id: 'filename',
      header: '文件名',
      size: 200,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('success_requests', {
      id: 'success_requests',
      header: '成功请求数',
      size: 120,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('benchmark_duration', {
      id: 'benchmark_duration',
      header: '基准测试时长(s)',
      size: 140,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('input_tokens', {
      id: 'input_tokens',
      header: '输入Token数',
      size: 120,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('output_tokens', {
      id: 'output_tokens',
      header: '输出Token数',
      size: 120,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('request_throughput', {
      id: 'request_throughput',
      header: '请求吞吐量(req/s)',
      size: 150,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('output_token_throughput', {
      id: 'output_token_throughput',
      header: '输出Token吞吐量(tok/s)',
      size: 180,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('total_token_throughput', {
      id: 'total_token_throughput',
      header: '总Token吞吐量(tok/s)',
      size: 170,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('avg_ttft', {
      id: 'avg_ttft',
      header: '平均TTFT(ms)',
      size: 120,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('median_ttft', {
      id: 'median_ttft',
      header: '中位TTFT(ms)',
      size: 130,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('p99_ttft', {
      id: 'p99_ttft',
      header: 'P99 TTFT(ms)',
      size: 130,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('avg_tpot', {
      id: 'avg_tpot',
      header: '平均TPOT(ms)',
      size: 120,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('median_tpot', {
      id: 'median_tpot',
      header: '中位TPOT(ms)',
      size: 130,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('p99_tpot', {
      id: 'p99_tpot',
      header: 'P99 TPOT(ms)',
      size: 130,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.display({
      id: 'actions',
      header: '操作',
      size: 120,
      cell: ({ row }) => (
        <Space size="small">
          <Tooltip title="删除记录">
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => deleteRecord(row.original)}
            />
          </Tooltip>
        </Space>
      )
    })
  ], [editingCell, editingValue, startEditing]);

  // 创建表格实例
  const table = useReactTable({
    data,
    columns,
    state: {
      globalFilter,
      sorting,
    },
    onGlobalFilterChange: setGlobalFilter,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    globalFilterFn: 'includesString',
  });

  // 添加空行
  const addEmptyRow = () => {
    const maxId = Math.max(
      ...data.map(row => parseInt(row.id)).filter(id => !isNaN(id)),
      0
    );
    const newId = maxId + 1;

    const newRecord = {
      id: newId,
      filename: '',
      success_requests: null,
      benchmark_duration: null,
      input_tokens: null,
      output_tokens: null,
      request_throughput: null,
      output_token_throughput: null,
      total_token_throughput: null,
      avg_ttft: null,
      median_ttft: null,
      p99_ttft: null,
      avg_tpot: null,
      median_tpot: null,
      p99_tpot: null,
      _modified: true,
      _isNew: true
    };

    setData([...data, newRecord]);
    setHasUnsavedChanges(true);
    message.success('已添加空行，点击单元格可编辑');
  };

  // 保存所有更改
  const saveAllChanges = async () => {
    const modifiedData = data.filter(item => item._modified);

    if (modifiedData.length === 0) {
      message.warning('没有修改的数据需要保存');
      return;
    }

    try {
      setLoading(true);

      for (const item of modifiedData) {
        // 处理空值，将空字符串和null转换为合适的值
        const saveData = {
          model_name: modelName,
          filename: item.filename || '',
          success_requests: item.success_requests === '' || item.success_requests === null ? null : item.success_requests,
          benchmark_duration: item.benchmark_duration === '' || item.benchmark_duration === null ? null : item.benchmark_duration,
          input_tokens: item.input_tokens === '' || item.input_tokens === null ? null : item.input_tokens,
          output_tokens: item.output_tokens === '' || item.output_tokens === null ? null : item.output_tokens,
          request_throughput: item.request_throughput === '' || item.request_throughput === null ? null : item.request_throughput,
          output_token_throughput: item.output_token_throughput === '' || item.output_token_throughput === null ? null : item.output_token_throughput,
          total_token_throughput: item.total_token_throughput === '' || item.total_token_throughput === null ? null : item.total_token_throughput,
          avg_ttft: item.avg_ttft === '' || item.avg_ttft === null ? null : item.avg_ttft,
          median_ttft: item.median_ttft === '' || item.median_ttft === null ? null : item.median_ttft,
          p99_ttft: item.p99_ttft === '' || item.p99_ttft === null ? null : item.p99_ttft,
          avg_tpot: item.avg_tpot === '' || item.avg_tpot === null ? null : item.avg_tpot,
          median_tpot: item.median_tpot === '' || item.median_tpot === null ? null : item.median_tpot,
          p99_tpot: item.p99_tpot === '' || item.p99_tpot === null ? null : item.p99_tpot,
        };

        if (item._isNew) {
          // 新增记录
          await http.post('/api/model-storage/performance-test-data/', saveData);
        } else {
          // 更新记录
          await http.put(`/api/model-storage/performance-test-data/${item.id}/`, saveData);
        }
      }

      message.success(`成功保存 ${modifiedData.length} 条记录`);
      setHasUnsavedChanges(false);

      // 重新获取数据以确保同步
      await fetchData();

    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败，请检查数据格式');
    } finally {
      setLoading(false);
    }
  };

  // 导出Excel
  const exportToExcel = () => {
    if (!data || data.length === 0) {
      message.warning('暂无数据可导出');
      return;
    }

    // 准备导出数据，移除内部字段
    const exportData = data.map(item => {
      const { id, _modified, _isNew, ...rest } = item;
      return rest;
    });

    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, modelName || '模型数据');

    const fileName = `${modelName || '模型数据'}_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);
    message.success('导出成功');
  };

  // 导入Excel
  const importFromExcel = (file) => {
    // 检查文件大小（限制为10MB）
    if (file.size > 10 * 1024 * 1024) {
      message.error('文件大小不能超过10MB');
      return false;
    }

    // 显示加载状态
    const loadingMessage = message.loading('正在导入Excel文件...', 0);

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const arrayBuffer = e.target.result;
        const uint8Array = new Uint8Array(arrayBuffer);
        const workbook = XLSX.read(uint8Array, { type: 'array' });

        if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
          loadingMessage();
          message.error('Excel文件中没有工作表');
          return;
        }

        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        if (jsonData.length === 0) {
          loadingMessage();
          message.warning('Excel文件中没有数据');
          return;
        }

        // 限制导入数量
        if (jsonData.length > 1000) {
          loadingMessage();
          message.error('一次最多只能导入1000条数据');
          return;
        }

        // 计算最大ID（优化性能）- 使用当前表格数据
        let maxId = 0;
        if (data.length > 0) {
          for (const row of data) {
            const id = parseInt(row.id);
            if (!isNaN(id) && id > maxId) {
              maxId = id;
            }
          }
        }

        // 使用 setTimeout 异步处理数据转换，避免阻塞UI
        setTimeout(() => {
          try {
            // 转换导入的数据格式
            const importedData = jsonData.map((item, index) => {
              return {
                id: maxId + index + 1,
                filename: item.filename || '',
                success_requests: item.success_requests || null,
                benchmark_duration: item.benchmark_duration || null,
                input_tokens: item.input_tokens || null,
                output_tokens: item.output_tokens || null,
                request_throughput: item.request_throughput || null,
                output_token_throughput: item.output_token_throughput || null,
                total_token_throughput: item.total_token_throughput || null,
                avg_ttft: item.avg_ttft || null,
                median_ttft: item.median_ttft || null,
                p99_ttft: item.p99_ttft || null,
                avg_tpot: item.avg_tpot || null,
                median_tpot: item.median_tpot || null,
                p99_tpot: item.p99_tpot || null,
                _modified: true,
                _isNew: true
              };
            });

            setData(prevData => [...prevData, ...importedData]);
            setHasUnsavedChanges(true);

            // 关闭加载消息并显示成功消息
            loadingMessage();
            message.success(`成功导入 ${importedData.length} 条数据`);
          } catch (error) {
            console.error('数据转换失败:', error);
            loadingMessage();
            message.error('数据转换失败');
          }
        }, 100);
      } catch (error) {
        console.error('导入失败:', error);
        // 关闭加载消息并显示错误消息
        loadingMessage();
        message.error('Excel文件格式错误，导入失败');
      }
    };

    reader.onerror = () => {
      loadingMessage();
      message.error('文件读取失败');
    };

    reader.readAsArrayBuffer(file);
    return false; // 阻止默认上传行为
  };

  // 删除记录
  const deleteRecord = (record) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除文件 "${record.filename}" 的记录吗？`,
      onOk: async () => {
        try {
          if (!record._isNew && record.id) {
            await http.delete(`/api/model-storage/performance-test-data/${record.id}/`);
          }
          setData(data.filter(item => item.id !== record.id));
          setHasUnsavedChanges(true);
          message.success('删除成功');
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败');
        }
      }
    });
  };

  return (
    <div className={styles.container}>
      <Card 
        title={`模型数据表格 - ${modelName}`}
        extra={
          <Space>
            <Search
              placeholder="搜索..."
              value={globalFilter}
              onChange={(e) => setGlobalFilter(e.target.value)}
              style={{ width: 200 }}
              allowClear
            />
            <Button
              icon={<PlusOutlined />}
              onClick={addEmptyRow}
            >
              添加行
            </Button>
            <Upload
              accept=".xlsx,.xls"
              beforeUpload={importFromExcel}
              showUploadList={false}
            >
              <Button icon={<UploadOutlined />}>
                导入Excel
              </Button>
            </Upload>
            <Button
              icon={<SaveOutlined />}
              type="primary"
              disabled={!hasUnsavedChanges}
              onClick={saveAllChanges}
              loading={loading}
            >
              保存更改 {hasUnsavedChanges && `(${data.filter(item => item._modified).length})`}
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={exportToExcel}
            >
              导出Excel
            </Button>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={fetchData}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        }
      >
        <Spin spinning={loading}>
          <div className={styles.tableWrapper}>
            <table className={styles.table}>
              <thead>
                {table.getHeaderGroups().map(headerGroup => (
                  <tr key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <th 
                        key={header.id}
                        style={{ width: header.getSize() }}
                        className={styles.tableHeader}
                      >
                        <div
                          className={`${styles.headerContent} ${
                            header.column.getCanSort() ? styles.sortable : ''
                          }`}
                          onClick={header.column.getToggleSortingHandler()}
                        >
                          {flexRender(header.column.columnDef.header, header.getContext())}
                          {header.column.getIsSorted() && (
                            <span className={styles.sortIcon}>
                              {header.column.getIsSorted() === 'desc' ? '↓' : '↑'}
                            </span>
                          )}
                        </div>
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody>
                {table.getRowModel().rows.map(row => (
                  <tr 
                    key={row.id} 
                    className={`${styles.tableRow} ${
                      row.original._modified ? styles.modifiedRow : ''
                    }`}
                  >
                    {row.getVisibleCells().map(cell => (
                      <td key={cell.id} className={styles.tableCell}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Spin>
      </Card>
    </div>
  );
}

export default ModelDataTableTanStack;

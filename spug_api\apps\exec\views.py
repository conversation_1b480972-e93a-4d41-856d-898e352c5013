# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.
from django.views.generic import View
from django.db import close_old_connections, transaction
from django.conf import settings
from libs import JsonParser, Argument, json_response, auth, human_datetime
from apps.account.utils import has_host_perm
from apps.host.models import Host
from apps.setting.utils import AppSetting
from apps.app.models import Deploy
from apps.schedule.models import Task
from apps.notify.models import Notify
from apps.account.models import User
from apps.exec.models import ExecTemplate, TestPlan, TestCaseSet
from libs.ssh import SSH
from concurrent import futures
from datetime import datetime
from threading import Thread
from functools import partial
import json
import json
import socket
import subprocess
import json
import uuid
import os


class TaskView(View):
    @auth('exec.task.do')
    def get(self, request):
        host_id = request.GET.get('host_id')
        if not host_id:
            return json_response(error='未指定主机')
        history = []
        for item in request.user.exec_task.all():
            data = item.to_dict()
            if not data['is_public'] and data['created_by_id'] != request.user.id:
                continue
            data['alias'] = item.host.name if item.host_id else None
            if host_id and item.host_id != int(host_id):
                continue
            data['command'] = item.command
            history.append(data)
        return json_response(history)

    @auth('exec.task.do')
    def post(self, request):
        form, error = JsonParser(
            Argument('host_id', type=int, help='请选择主机'),
            Argument('command', help='请输入要执行的命令')
        ).parse(request.body)
        if error:
            return json_response(error=error)

        host = Host.objects.filter(pk=form.host_id).first()
        if not host:
            return json_response(error='未找到指定主机')
        if not has_host_perm(request.user, host.id):
            return json_response(error='您无权访问该主机')

        task = request.user.exec_task.create(
            host_id=form.host_id,
            command=form.command,
            is_public=False
        )
        return json_response(task.id)

    @auth('exec.task.edit|exec.task.del')
    def patch(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='请指定操作对象'),
            Argument('is_public', type=bool, required=False)
        ).parse(request.body)
        if error:
            return json_response(error=error)

        task = request.user.exec_task.filter(pk=form.id).first()
        if not task:
            return json_response(error='未找到指定记录')

        task.is_public = form.is_public
        task.save()
        return json_response()

    @auth('exec.task.del')
    def delete(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='请指定操作对象')
        ).parse(request.GET)
        if error:
            return json_response(error=error)

        task = request.user.exec_task.filter(pk=form.id).first()
        if task:
            task.delete()
        return json_response()


class BatchView(View):
    @auth('exec.task.batch')
    def post(self, request):
        form, error = JsonParser(
            Argument('host_ids', type=list, filter=lambda x: len(x), help='请选择执行主机'),
            Argument('command', help='请输入要执行的命令'),
            Argument('interpreter', default='sh')
        ).parse(request.body)
        if error:
            return json_response(error=error)

        if not has_host_perm(request.user, form.host_ids):
            return json_response(error='您无权访问该主机')

        token = uuid.uuid4().hex
        outputs = {}
        for host_id in form.host_ids:
            outputs[host_id] = {'id': host_id, 'output': f'### Executing: {form.command}\n'}

        def do_exec(host_id):
            host = Host.objects.get(pk=host_id)
            cli = host.get_ssh()
            code = -1
            try:
                code, out = cli.exec_command(form.command, form.interpreter)
                outputs[host.id]['output'] += out
            except Exception as e:
                outputs[host.id]['output'] += f'Exception: {e}\n'
            finally:
                outputs[host.id]['status'] = 'success' if code == 0 else 'error'
                outputs[host.id]['output'] += f'\n### Exit code: {code}\n'
                cli.close()

        threads = []
        for host_id in form.host_ids:
            t = Thread(target=do_exec, args=(host_id,))
            threads.append(t)
            t.start()
        for t in threads:
            t.join()

        return json_response({'token': token, 'outputs': outputs})


class BatchCommand(View):
    @auth('exec.task.batch')
    def post(self, request):
        form, error = JsonParser(
            Argument('host_ids', type=list, filter=lambda x: len(x), help='请选择执行主机'),
            Argument('command', help='请输入要执行的命令'),
            Argument('interpreter', default='sh')
        ).parse(request.body)
        if error:
            return json_response(error=error)

        if not has_host_perm(request.user, form.host_ids):
            return json_response(error='您无权访问该主机')

        token = uuid.uuid4().hex
        outputs = {}
        for host_id in form.host_ids:
            outputs[host_id] = {'id': host_id, 'output': f'### Executing: {form.command}\n'}

        def do_exec(host_id):
            host = Host.objects.get(pk=host_id)
            cli = host.get_ssh()
            code = -1
            try:
                code, out = cli.exec_command(form.command, form.interpreter)
                outputs[host.id]['output'] += out
            except Exception as e:
                outputs[host.id]['output'] += f'Exception: {e}\n'
            finally:
                outputs[host.id]['status'] = 'success' if code == 0 else 'error'
                outputs[host.id]['output'] += f'\n### Exit code: {code}\n'
                cli.close()

        threads = []
        for host_id in form.host_ids:
            t = Thread(target=do_exec, args=(host_id,))
            threads.append(t)
            t.start()
        for t in threads:
            t.join()

        return json_response({'token': token, 'outputs': outputs})


class TemplateView(View):
    @auth('exec.template.view')
    def get(self, request):
        templates = ExecTemplate.objects.all()
        types = [x['type'] for x in templates.order_by('type').values('type').distinct()]
        return json_response({'types': types, 'templates': [x.to_dict() for x in templates]})

    @auth('exec.template.add')
    def post(self, request):
        form, error = JsonParser(
            Argument('name', help='请输入模版名称'),
            Argument('type', help='请选择模版类型'),
            Argument('body', help='请输入模版内容'),
            Argument('interpreter', default='sh'),
            Argument('host_ids', type=list, default=[]),
            Argument('desc', required=False)
        ).parse(request.body)
        if error:
            return json_response(error=error)

        if ExecTemplate.objects.filter(name=form.name, type=form.type).exists():
            return json_response(error=f'已存在的模板名称【{form.name}】')

        ExecTemplate.objects.create(
            name=form.name,
            type=form.type,
            body=form.body,
            interpreter=form.interpreter,
            host_ids=json.dumps(form.host_ids),
            desc=form.desc
        )
        return json_response()

    @auth('exec.template.edit')
    def patch(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='请指定操作对象'),
            Argument('name', help='请输入模版名称'),
            Argument('type', help='请选择模版类型'),
            Argument('body', help='请输入模版内容'),
            Argument('interpreter', default='sh'),
            Argument('host_ids', type=list, default=[]),
            Argument('desc', required=False)
        ).parse(request.body)
        if error:
            return json_response(error=error)

        if ExecTemplate.objects.filter(name=form.name, type=form.type).exclude(id=form.id).exists():
            return json_response(error=f'已存在的模板名称【{form.name}】')

        ExecTemplate.objects.filter(pk=form.id).update(
            name=form.name,
            type=form.type,
            body=form.body,
            interpreter=form.interpreter,
            host_ids=json.dumps(form.host_ids),
            desc=form.desc
        )
        return json_response()

    @auth('exec.template.del')
    def delete(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='请指定操作对象')
        ).parse(request.GET)
        if error:
            return json_response(error=error)

        ExecTemplate.objects.filter(pk=form.id).delete()
        return json_response()


class FileView(View):
    @auth('exec.task.do')
    def post(self, request):
        form, error = JsonParser(
            Argument('host_id', type=int, help='请选择主机'),
            Argument('path', help='请输入文件路径'),
            Argument('token', help='请输入验证令牌')
        ).parse(request.body)
        if error:
            return json_response(error=error)

        if not has_host_perm(request.user, form.host_id):
            return json_response(error='您无权访问该主机')

        host = Host.objects.filter(pk=form.host_id).first()
        if not host:
            return json_response(error='未找到指定主机')

        cli = host.get_ssh()
        try:
            with cli.get_client() as c:
                sftp = c.open_sftp()
                f_stat = sftp.stat(form.path)

                if not request.user.is_supper:
                    if stat.S_ISDIR(f_stat.st_mode):
                        return json_response(error='该路径是一个目录，需要超级管理员权限查看目录')

                    if not request.user.has_host_perm(host.id):
                        return json_response(error='您无权访问该主机')

                if stat.S_ISDIR(f_stat.st_mode):
                    return json_response(error='该路径是一个目录，请输入文件路径')

                if f_stat.st_size > 1024 * 1024 * 10:
                    return json_response(error='文件大小超过10M，请使用下载功能')

                f = sftp.open(form.path)
                content = f.read().decode(errors='ignore')
                f.close()
                return json_response(content)
        except Exception as e:
            return json_response(error=f'读取文件失败：{e}')
        finally:
            cli.close()


class ExecView(View):
    @auth('exec.task.do')
    def post(self, request):
        form, error = JsonParser(
            Argument('host_id', type=int, help='请选择主机'),
            Argument('command', help='请输入要执行的命令'),
            Argument('interpreter', default='sh')
        ).parse(request.body)
        if error:
            return json_response(error=error)

        host = Host.objects.filter(pk=form.host_id).first()
        if not host:
            return json_response(error='未找到指定主机')
        if not has_host_perm(request.user, host.id):
            return json_response(error='您无权访问该主机')

        command = form.command
        try:
            with host.get_ssh() as cli:
                code, out = cli.exec_command_raw(command)
                success = code == 0
                return json_response({
                    'success': success,
                    'data': out,
                    'exit_code': code,
                    'command': command,
                    'error': None if success else out
                })
        except Exception as e:
            return json_response(error=f'执行失败：{e}')


class TransferView(View):
    @auth('exec.transfer.do')
    def post(self, request):
        form, error = JsonParser(
            Argument('host_id', type=int, help='请选择主机'),
            Argument('src_path', help='请输入源路径'),
            Argument('dst_path', help='请输入目标路径'),
            Argument('host_type', help='请选择操作类型'),
        ).parse(request.body)
        if error:
            return json_response(error=error)

        host = Host.objects.filter(pk=form.host_id).first()
        if not host:
            return json_response(error='未找到指定主机')
        if not has_host_perm(request.user, host.id):
            return json_response(error='您无权访问该主机')

        cli = host.get_ssh()
        try:
            if form.host_type == 'parse':
                return json_response(cli.list_dir_attr(form.src_path))
            elif form.host_type == 'get':
                return json_response(cli.download_file(form.src_path, form.dst_path))
            elif form.host_type == 'put':
                return json_response(cli.upload_file(form.src_path, form.dst_path))
            else:
                return json_response(error='未识别的操作类型')
        except Exception as e:
            return json_response(error=f'传输失败：{e}')
        finally:
            cli.close()


class WebTerminalView(View):
    @auth('exec.console.view|exec.console.list')
    def get(self, request):
        form, error = JsonParser(
            Argument('id', type=int, required=False),
            Argument('type', required=False),
            Argument('aid', type=int, required=False),
            Argument('app_id', type=int, required=False),
            Argument('env_id', type=int, required=False),
        ).parse(request.GET)

        if not request.user.has_perms(['exec.console.view', 'exec.console.list']):
            return json_response(error='权限拒绝')

        if not form.id:
            return json_response(error='请指定主机id')

        host = Host.objects.filter(pk=form.id).first()
        if not host:
            return json_response(error='未找到指定主机')
        if not has_host_perm(request.user, host.id):
            return json_response(error='您无权访问该主机')

        try:
            cli = host.get_ssh()
            cli.ping()
        except Exception as e:
            return json_response(error=f'ssh连接失败：{e}')

        if form.type == 'exec':
            if not request.user.has_perms(['exec.console.list']):
                return json_response(error='权限拒绝')

            if form.aid:
                deploy = Deploy.objects.filter(pk=form.aid).first()
                if not deploy:
                    return json_response(error='未找到指定应用')
                if not deploy.extend_obj.env.id == form.env_id:
                    return json_response(error='错误的环境ID')
                command = deploy.extend_obj.hook_plan
            else:
                command = AppSetting.get_default('exec_command')
            return json_response({
                'token': uuid.uuid4().hex,
                'type': 'exec',
                'command': command
            })
        else:
            if not request.user.has_perms(['exec.console.view']):
                return json_response(error='权限拒绝')

            if host.username == 'root' and not host.pkey:
                return json_response(error='禁止以root用户直接登录，请参考官方文档部署密钥登录')

            token = uuid.uuid4().hex
            return json_response({
                'token': token,
                'type': 'web',
                'size': request.GET.get('size', '80x24'),
                'host': host.hostname,
                'port': host.port,
                'username': host.username,
                'password': host.password,
                'pkey': host.pkey,
            })

    @auth('exec.console.view')
    def post(self, request):
        form, error = JsonParser(
            Argument('token', help='参数错误'),
            Argument('hostname', help='参数错误'),
            Argument('port', type=int, help='参数错误'),
            Argument('username', help='参数错误'),
            Argument('password', required=False),
            Argument('pkey', required=False),
            Argument('size', default='80x24'),
        ).parse(request.body)

        if error:
            return json_response(error=error)

        try:
            cli = SSH(form.hostname, form.port, form.username, form.password, form.pkey)
            cli.ping()
        except Exception as e:
            return json_response(error=f'ssh连接失败：{e}')

        width, height = form.size.split('x')
        width, height = int(width), int(height)
        options = {
            'custom_log': False,
            'token': form.token,
            'width': width,
            'height': height
        }
        try:
            websocket = WebTerminalConsumer(options)
            websocket.init_session(cli)
            return json_response()
        except Exception as e:
            return json_response(error=f'创建会话失败：{e}')


class WebTerminalExecuteView(View):
    @auth('exec.console.list')
    def post(self, request):
        form, error = JsonParser(
            Argument('token', help='参数错误'),
            Argument('hostname', help='参数错误'),
            Argument('port', type=int, help='参数错误'),
            Argument('username', help='参数错误'),
            Argument('command', help='参数错误'),
            Argument('password', required=False),
            Argument('pkey', required=False),
        ).parse(request.body)

        if error:
            return json_response(error=error)

        try:
            cli = SSH(form.hostname, form.port, form.username, form.password, form.pkey)
            cli.ping()
        except Exception as e:
            return json_response(error=f'ssh连接失败：{e}')

        options = {
            'custom_log': False,
            'token': form.token,
            'width': 80,
            'height': 24
        }
        try:
            websocket = WebTerminalExecuteConsumer(options)
            websocket.init_session(cli, form.command)
            return json_response()
        except Exception as e:
            return json_response(error=f'创建会话失败：{e}')


class WebTerminalCloseView(View):
    @auth('exec.console.view')
    def post(self, request):
        form, error = JsonParser(
            Argument('token', help='参数错误'),
        ).parse(request.body)

        if error:
            return json_response(error=error)

        try:
            ws = WebTerminalConsumer.find_by_token(form.token)
            ws.disconnect(True)
        except:
            pass
        finally:
            return json_response()


class TestResultView(View):
    """测试结果收集API"""

    def get(self, request, result_id=None):
        # 检查是否是对比API调用
        if 'compare' in request.path:
            return self.compare_results(request)
        """获取测试结果列表或单个测试结果"""
        # 检查开发者后门token (支持URL参数和请求头两种方式)
        token = request.GET.get('token') or request.META.get('HTTP_TOKEN')
        with open('api_debug.txt', 'a', encoding='utf-8') as f:
            f.write(f"[DEBUG] TestResultView.get called, token={token}, result_id={result_id}\n")
        print(f"[DEBUG] TestResultView.get called, token={token}, result_id={result_id}")
        if token == '1':
            # 开发者模式，跳过权限验证，返回真实数据
            try:
                from .models import TestResult

                if result_id:
                    # 获取单个测试结果
                    try:
                        result = TestResult.objects.get(pk=result_id)
                        return json_response(result.to_dict())
                    except TestResult.DoesNotExist:
                        return json_response(error='测试结果不存在')
                else:
                    # 获取测试结果列表
                    plan_name = request.GET.get('plan_name')
                    gpu_name = request.GET.get('gpu_name')
                    source_type = request.GET.get('source_type')
                    date_from = request.GET.get('date_from')
                    date_to = request.GET.get('date_to')

                    query = TestResult.objects.all()

                    if plan_name:
                        query = query.filter(plan_name__icontains=plan_name)
                    if gpu_name:
                        query = query.filter(gpu_name__icontains=gpu_name)
                    if source_type:
                        query = query.filter(source_type=source_type)
                    if date_from and date_to:
                        from datetime import datetime
                        try:
                            date_from = datetime.strptime(date_from, '%Y-%m-%d')
                            date_to = datetime.strptime(date_to + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                            query = query.filter(created_at__range=(date_from, date_to))
                        except ValueError:
                            pass

                    results = [result.to_dict() for result in query]
                    with open('api_debug.txt', 'a', encoding='utf-8') as f:
                        f.write(f"[DEBUG] Returning {len(results)} results: {results}\n")
                    print(f"[DEBUG] Returning {len(results)} results: {results}")

                    # 调试json_response的返回内容
                    response = json_response(results)
                    with open('api_debug.txt', 'a', encoding='utf-8') as f:
                        f.write(f"[DEBUG] json_response content: {response.content.decode('utf-8')}\n")
                    print(f"[DEBUG] json_response content: {response.content.decode('utf-8')}")
                    return response

            except Exception as e:
                return json_response(error=f'获取测试结果失败: {str(e)}')

            # 如果上面的真实数据获取失败，返回模拟数据作为备用
            mock_data = [
                {
                    "id": 1,
                    "plan_name": "性能测试计划A",
                    "task_name": "CPU压力测试",
                    "source_type": "execution",
                    "total_metrics": 3,
                    "confirmed_metrics": 2,
                    "ai_confidence": 0.85,
                    "created_at": "2024-01-15 10:30:00",
                    "updated_at": "2024-01-15 10:35:00",
                    "metrics": [
                        {"name": "CPU使用率", "value": "75%", "unit": "%", "threshold": "80%", "status": "正常"},
                        {"name": "内存使用率", "value": "60%", "unit": "%", "threshold": "70%", "status": "正常"},
                        {"name": "响应时间", "value": "120ms", "unit": "ms", "threshold": "200ms", "status": "正常"}
                    ]
                },
                {
                    "id": 2,
                    "plan_name": "性能测试计划B",
                    "task_name": "内存压力测试",
                    "source_type": "log_extraction",
                    "total_metrics": 4,
                    "confirmed_metrics": 3,
                    "ai_confidence": 0.92,
                    "created_at": "2024-01-15 11:00:00",
                    "updated_at": "2024-01-15 11:05:00",
                    "metrics": [
                        {"name": "内存使用率", "value": "85%", "unit": "%", "threshold": "90%", "status": "正常"},
                        {"name": "磁盘IO", "value": "45MB/s", "unit": "MB/s", "threshold": "50MB/s", "status": "正常"},
                        {"name": "网络延迟", "value": "15ms", "unit": "ms", "threshold": "20ms", "status": "正常"},
                        {"name": "吞吐量", "value": "1200req/s", "unit": "req/s", "threshold": "1000req/s", "status": "优秀"}
                    ]
                },
                {
                    "id": 3,
                    "plan_name": "稳定性测试计划",
                    "task_name": "长时间运行测试",
                    "source_type": "execution",
                    "total_metrics": 2,
                    "confirmed_metrics": 1,
                    "ai_confidence": 0.78,
                    "created_at": "2024-01-15 12:00:00",
                    "updated_at": "2024-01-15 12:30:00",
                    "metrics": [
                        {"name": "系统稳定性", "value": "99.5%", "unit": "%", "threshold": "99%", "status": "优秀"},
                        {"name": "错误率", "value": "0.1%", "unit": "%", "threshold": "1%", "status": "正常"}
                    ]
                }
            ]
            
            if result_id:
                # 返回单个测试结果
                for item in mock_data:
                    if item["id"] == int(result_id):
                        return json_response(item)
                return json_response(error='测试结果不存在')
            else:
                # 返回测试结果列表
                return json_response(mock_data)
        
        # 正常权限验证流程
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return json_response(error='验证失败，请重新登录', status=401)
        
        # 检查权限
        if not request.user.has_perms(['exec.task.do']):
            return json_response(error='权限不足', status=403)
        
        try:
            from .models import TestResult
            
            if result_id:
                # 获取单个测试结果
                try:
                    result = TestResult.objects.get(pk=result_id)
                    
                    # 获取并处理指标数据
                    metrics = result.get_metrics()
                    
                    return json_response(result.to_dict())
                except TestResult.DoesNotExist:
                    return json_response(error='测试结果不存在')
            else:
                # 获取测试结果列表
                plan_name = request.GET.get('plan_name')
                gpu_name = request.GET.get('gpu_name')
                model_name = request.GET.get('model_name')
                source_type = request.GET.get('source_type')
                date_from = request.GET.get('date_from')
                date_to = request.GET.get('date_to')

                query = TestResult.objects.all()

                if plan_name:
                    query = query.filter(plan_name__icontains=plan_name)
                if gpu_name:
                    query = query.filter(gpu_name__icontains=gpu_name)
                if model_name:
                    query = query.filter(model_name__icontains=model_name)
                if source_type:
                    query = query.filter(source_type=source_type)
                if date_from and date_to:
                    from datetime import datetime
                    try:
                        date_from = datetime.strptime(date_from, '%Y-%m-%d')
                        date_to = datetime.strptime(date_to + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                        query = query.filter(created_at__range=(date_from, date_to))
                    except ValueError:
                        pass
                
                results = [result.to_dict() for result in query]
                
                return json_response(results)
                
        except Exception as e:
            return json_response(error=f'获取测试结果失败: {str(e)}')
    
    @auth('exec.task.do')
    def put(self, request, result_id):
        """更新测试结果"""
        try:
            from .models import TestResult
            from django.utils import timezone
            
            # 获取测试结果
            try:
                result = TestResult.objects.get(pk=result_id)
            except TestResult.DoesNotExist:
                return json_response(error=f'测试结果ID {result_id} 不存在')
            
            # 解析请求数据
            data = json.loads(request.body)
            plan_name = data.get('plan_name')
            gpu_name = data.get('gpu_name')
            model_name = data.get('model_name')
            confirmed_metrics = data.get('confirmed_metrics')

            # 更新测试结果
            if plan_name is not None:
                result.plan_name = plan_name
            if gpu_name is not None:
                result.gpu_name = gpu_name
            if model_name is not None:
                result.model_name = model_name
            if confirmed_metrics is not None:
                result.confirmed_metrics = confirmed_metrics
            
            # 更新时间
            result.updated_at = timezone.now()
            result.save()
            
            return json_response({'message': '测试结果已更新'})
        except Exception as e:
            return json_response(error=f'更新测试结果失败: {str(e)}')
    
    @auth('exec.task.do')
    def post(self, request):
        """保存测试结果"""
        try:
            from .models import TestResult, TestPlanExecution
            from django.db import transaction
            from django.utils import timezone
            from libs import human_datetime
            
            data = json.loads(request.body)
            execution_id = data.get('execution_id')
            plan_name = data.get('plan_name')
            gpu_name = data.get('gpu_name', '')
            model_name = data.get('model_name', '')
            metrics = data.get('metrics', [])
            raw_log = data.get('raw_log', '')
            log_path = data.get('log_path', '')
            confirmed_metrics = data.get('confirmed_metrics', 0)
            ai_confidence = data.get('ai_confidence', 0.0)
            source = data.get('source', '')
            
            # 获取额外参数
            log_source_host = data.get('log_source_host', '')
            log_source_path = data.get('log_source_path', '')
            log_extraction_method = data.get('log_extraction_method', '')
            source_type = data.get('source_type', 'execution')
            
            # 检查是否为独立日志提取模式
            is_standalone = source == 'remote_extraction' or source_type == 'log_extraction' or not execution_id

            # 验证必要参数 - 独立日志提取模式下plan_name可选
            if not is_standalone and not plan_name:
                return json_response(error='缺少测试计划名称')
            
            if not metrics:
                return json_response(error='请至少提供一个性能指标')
            
            # 准备metrics数据
            metrics_json = json.dumps(metrics, ensure_ascii=False)
            
            # 使用Django ORM和事务来避免数据库锁定问题
            with transaction.atomic():
                try:
                    execution = None
                    user_id = request.user.id if hasattr(request, 'user') and request.user.id else 1
                    
                    if not is_standalone and execution_id:
                        # 测试计划执行模式 - 验证执行记录ID
                        try:
                            execution = TestPlanExecution.objects.get(pk=execution_id)
                            user_id = getattr(execution, 'executor_id', 1)
                            source_type = 'execution'
                        except TestPlanExecution.DoesNotExist:
                            # 列出所有可用的TestPlanExecution记录
                            all_executions = TestPlanExecution.objects.all()
                            available_ids = [str(h.id) for h in all_executions]
                            return json_response(error=f'执行记录ID {execution_id} 不存在。可用的ID: {", ".join(available_ids)}')
                    else:
                        # 独立日志提取模式
                        execution_id = None
                        if not source_type or source_type == 'execution':
                            source_type = 'log_extraction'
                    
                    # 检查是否已有相同的测试结果
                    if execution_id:
                        existing_result = TestResult.objects.filter(execution_id=execution_id).first()
                    else:
                        # 对于独立模式，检查是否有相同计划名称和GPU名称的结果
                        existing_result = TestResult.objects.filter(
                            execution__isnull=True,
                            plan_name=plan_name,
                            gpu_name=gpu_name,
                            source_type=source_type
                        ).first()
                    
                    if existing_result:
                        # 更新现有结果
                        existing_result.plan_name = plan_name
                        existing_result.gpu_name = gpu_name
                        existing_result.model_name = model_name
                        existing_result.source_type = source_type
                        existing_result.log_source_host = log_source_host
                        existing_result.log_source_path = log_source_path
                        existing_result.log_extraction_method = log_extraction_method
                        existing_result.metrics = metrics_json
                        existing_result.raw_log = raw_log or existing_result.raw_log
                        existing_result.total_metrics = len(metrics)
                        existing_result.confirmed_metrics = confirmed_metrics
                        existing_result.ai_confidence = ai_confidence
                        existing_result.updated_at = timezone.now()
                        existing_result.save()
                        
                        return json_response({
                            'message': '测试结果已更新',
                            'result_id': existing_result.id
                        })
                    else:
                        # 创建新的测试结果
                        new_result = TestResult.objects.create(
                            execution=execution,  # 可能为None
                            plan_name=plan_name,
                            gpu_name=gpu_name,
                            model_name=model_name,
                            source_type=source_type,
                            log_source_host=log_source_host,
                            log_source_path=log_source_path,
                            log_extraction_method=log_extraction_method,
                            metrics=metrics_json,
                            raw_log=raw_log or log_path,  # 保存原始日志内容或日志路径
                            total_metrics=len(metrics),
                            confirmed_metrics=confirmed_metrics,
                            ai_confidence=ai_confidence,
                            created_by_id=user_id
                        )
                        
                        return json_response({
                            'message': '测试结果已保存',
                            'result_id': new_result.id
                        })
                        
                except Exception as e:
                    print(f"[ERROR] 保存测试结果失败: {str(e)}")
                    return json_response(error=f'保存测试结果失败: {str(e)}')
                
        except Exception as e:
            return json_response(error=f'保存测试结果失败: {str(e)}')

    def compare_results(self, request):
        """对比测试结果"""
        # 检查开发者后门token
        token = request.GET.get('token') or request.META.get('HTTP_TOKEN')
        if token != '1':
            # 正常权限验证
            if not hasattr(request, 'user') or not request.user.is_authenticated:
                return json_response(error='验证失败，请重新登录', status=401)
            if not request.user.has_perms(['exec.task.do']):
                return json_response(error='权限不足', status=403)

        try:
            from .models import TestResult
            import json

            result_ids = request.GET.get('result_ids', '')
            if not result_ids:
                return json_response(error='请提供要对比的测试结果ID')

            # 解析结果ID列表
            try:
                id_list = [int(id.strip()) for id in result_ids.split(',') if id.strip()]
            except ValueError:
                return json_response(error='测试结果ID格式错误')

            if len(id_list) < 2:
                return json_response(error='至少需要2个测试结果进行对比')
            if len(id_list) > 2:
                return json_response(error='最多只能对比2个测试结果')

            # 获取测试结果
            results = []
            for result_id in id_list:
                try:
                    result = TestResult.objects.get(pk=result_id)
                    results.append(result)
                except TestResult.DoesNotExist:
                    return json_response(error=f'测试结果ID {result_id} 不存在')

            # 检查是否都是日志提取类型
            for result in results:
                if result.source_type != 'log_extraction':
                    return json_response(error='只支持对比日志提取类型的测试结果')

            # 执行对比逻辑
            compare_data = self._perform_comparison(results)

            return json_response({
                'success': True,
                'data': compare_data
            })

        except Exception as e:
            return json_response(error=f'对比测试结果失败: {str(e)}')

    def _perform_comparison(self, results):
        """执行具体的对比逻辑"""
        import json

        # 解析每个结果的数据
        parsed_results = []
        for result in results:
            result_data = {
                'id': result.id,
                'plan_name': result.plan_name,
                'gpu_name': result.gpu_name,
                'created_at': result.created_at.strftime('%Y-%m-%d %H:%M:%S') if result.created_at else '',
                'total_metrics': result.total_metrics,
                'files': []
            }

            # 解析原始日志数据
            if result.raw_log:
                try:
                    raw_data = json.loads(result.raw_log)
                    if isinstance(raw_data, list):
                        result_data['files'] = raw_data
                    else:
                        # 如果不是列表，尝试从指标中构建
                        result_data['files'] = self._build_files_from_metrics(result)
                except json.JSONDecodeError:
                    # 如果解析失败，从指标中构建
                    result_data['files'] = self._build_files_from_metrics(result)
            else:
                result_data['files'] = self._build_files_from_metrics(result)

            parsed_results.append(result_data)

        # 找到所有唯一的文件名
        all_filenames = set()
        for result_data in parsed_results:
            for file_data in result_data['files']:
                all_filenames.add(file_data.get('filename', ''))

        # 为每个文件名创建对比数据
        file_comparisons = []
        baseline_result = None

        for filename in sorted(all_filenames):
            if not filename:
                continue

            file_comp = {
                'filename': filename,
                'data': []
            }

            # 收集每个结果中该文件的数据
            file_metrics = {}
            for result_data in parsed_results:
                file_data = next((f for f in result_data['files'] if f.get('filename') == filename), None)
                if file_data:
                    metrics = self._extract_metrics_from_file(file_data)
                    file_metrics[result_data['id']] = metrics
                    file_comp['data'].append({
                        'result_id': result_data['id'],
                        'metrics': metrics
                    })

            # 确定基准值（每个指标的最高值）
            if file_metrics:
                baseline_metrics = self._calculate_baseline_metrics(file_metrics)
                file_comp['baseline_metrics'] = baseline_metrics

            file_comparisons.append(file_comp)

        # 确定整体基准结果（拥有最多最高值的结果）
        baseline_result = self._determine_baseline_result(parsed_results, file_comparisons)

        # 标记基准结果
        for result_data in parsed_results:
            result_data['is_baseline'] = (result_data['id'] == baseline_result['id'])

        return {
            'results': parsed_results,
            'file_comparisons': file_comparisons,
            'baseline_result': baseline_result,
            'comparison_summary': self._generate_comparison_summary(parsed_results, file_comparisons)
        }

    def _build_files_from_metrics(self, result):
        """从测试结果的指标中构建文件数据"""
        metrics = result.get_metrics()
        if not metrics:
            return []

        # 创建一个虚拟文件条目
        file_data = {
            'filename': result.plan_name or f'结果_{result.id}',
            'successful_requests': '',
            'benchmark_duration': '',
            'total_input_tokens': '',
            'total_generated_tokens': '',
            'request_throughput': '',
            'output_token_throughput': '',
            'total_token_throughput': '',
            'mean_ttft': '',
            'median_ttft': '',
            'p99_ttft': '',
            'mean_tpot': '',
            'median_tpot': '',
            'p99_tpot': ''
        }

        # 映射指标到字段
        for metric in metrics:
            name = metric.get('name', '').lower()
            value = metric.get('value', '')

            if 'successful requests' in name:
                file_data['successful_requests'] = value
            elif 'benchmark duration' in name:
                file_data['benchmark_duration'] = value
            elif 'total input tokens' in name:
                file_data['total_input_tokens'] = value
            elif 'total generated tokens' in name:
                file_data['total_generated_tokens'] = value
            elif 'request throughput' in name:
                file_data['request_throughput'] = value
            elif 'output token throughput' in name:
                file_data['output_token_throughput'] = value
            elif 'total token throughput' in name:
                file_data['total_token_throughput'] = value
            elif 'mean ttft' in name:
                file_data['mean_ttft'] = value
            elif 'median ttft' in name:
                file_data['median_ttft'] = value
            elif 'p99 ttft' in name:
                file_data['p99_ttft'] = value
            elif 'mean tpot' in name:
                file_data['mean_tpot'] = value
            elif 'median tpot' in name:
                file_data['median_tpot'] = value
            elif 'p99 tpot' in name:
                file_data['p99_tpot'] = value

        return [file_data]

    def _extract_metrics_from_file(self, file_data):
        """从文件数据中提取指标"""
        metrics = {}
        metric_fields = [
            'successful_requests', 'benchmark_duration', 'total_input_tokens',
            'total_generated_tokens', 'request_throughput', 'output_token_throughput',
            'total_token_throughput', 'mean_ttft', 'median_ttft', 'p99_ttft',
            'mean_tpot', 'median_tpot', 'p99_tpot'
        ]

        for field in metric_fields:
            value = file_data.get(field, '')
            if value and value != '-':
                try:
                    # 尝试转换为数字
                    metrics[field] = float(value)
                except (ValueError, TypeError):
                    metrics[field] = value

        return metrics

    def _calculate_baseline_metrics(self, file_metrics):
        """计算基准指标（每个指标的最高值）"""
        baseline = {}

        # 获取所有指标名称
        all_metrics = set()
        for metrics in file_metrics.values():
            all_metrics.update(metrics.keys())

        # 为每个指标找到最高值
        for metric_name in all_metrics:
            max_value = None
            for metrics in file_metrics.values():
                if metric_name in metrics:
                    value = metrics[metric_name]
                    if isinstance(value, (int, float)):
                        if max_value is None or value > max_value:
                            max_value = value

            if max_value is not None:
                baseline[metric_name] = max_value

        return baseline

    def _determine_baseline_result(self, parsed_results, file_comparisons):
        """确定整体基准结果（拥有最多最高值的结果）"""
        result_scores = {}

        # 初始化分数
        for result_data in parsed_results:
            result_scores[result_data['id']] = 0

        # 计算每个结果的得分
        for file_comp in file_comparisons:
            baseline_metrics = file_comp.get('baseline_metrics', {})

            for data_entry in file_comp['data']:
                result_id = data_entry['result_id']
                metrics = data_entry['metrics']

                # 检查该结果有多少指标达到基准值
                for metric_name, baseline_value in baseline_metrics.items():
                    if metric_name in metrics and metrics[metric_name] == baseline_value:
                        result_scores[result_id] += 1

        # 找到得分最高的结果
        best_result_id = max(result_scores.keys(), key=lambda k: result_scores[k])
        return next(r for r in parsed_results if r['id'] == best_result_id)

    def _generate_comparison_summary(self, parsed_results, file_comparisons):
        """生成对比摘要"""
        total_files = len(file_comparisons)
        total_metrics = sum(len(fc.get('baseline_metrics', {})) for fc in file_comparisons)

        return {
            'total_files': total_files,
            'total_metrics': total_metrics,
            'results_count': len(parsed_results)
        }

    @auth('exec.task.do')
    def delete(self, request, result_id):
        """删除测试结果"""
        try:
            from .models import TestResult
            
            result = TestResult.objects.filter(pk=result_id).first()
            if not result:
                return json_response(error='测试结果不存在')
            
            result.delete()
            return json_response({'message': '测试结果已删除'})
        except Exception as e:
            return json_response(error=f'删除测试结果失败: {str(e)}')


class TestPlanView(View):
    """测试计划API"""
    
    def get(self, request, plan_id=None):
        """获取测试计划列表或单个测试计划"""
        try:
            # 检查是否需要替换变量
            replace_variables = request.GET.get('replace_variables', 'false').lower() == 'true'

            if plan_id:
                # 获取单个测试计划
                try:
                    plan = TestPlan.objects.get(pk=plan_id)
                    plan_dict = plan.to_dict()
                    if replace_variables:
                        # 这里可以添加变量替换逻辑，暂时直接返回
                        pass
                    return json_response(plan_dict)
                except TestPlan.DoesNotExist:
                    return json_response(error=f'测试计划ID {plan_id} 不存在')

            # 获取测试计划列表
            plans = TestPlan.objects.all().order_by('-id')
            plans_data = []
            for plan in plans:
                plan_dict = plan.to_dict()
                if replace_variables:
                    # 这里可以添加变量替换逻辑，暂时直接返回
                    pass
                plans_data.append(plan_dict)

            return json_response(plans_data)
        except Exception as e:
            return json_response(error=f'获取测试计划失败: {str(e)}')
    
    @auth('exec.task.do')
    def post(self, request):
        """创建新测试计划"""
        try:
            data = json.loads(request.body)
            name = data.get('name')
            description = data.get('description')
            category = data.get('category')
            commands = data.get('commands', [])
            files = data.get('files', [])
            steps = data.get('steps', [])
            step_interval = data.get('step_interval', 0)
            file_path_strict = data.get('file_path_strict', False)
            variables = data.get('variables', [])
            
            if not name:
                return json_response(error='测试计划名称不能为空')
            
            # 创建新测试计划
            plan = TestPlan.objects.create(
                name=name,
                description=description,
                category=category,
                commands=json.dumps(commands),
                files=json.dumps(files),
                steps=json.dumps(steps),
                step_interval=step_interval,
                file_path_strict=file_path_strict,
                variables=json.dumps(variables),
                created_at=human_datetime(),
                created_by_id=request.user.id if hasattr(request, 'user') else 1,
                updated_at=human_datetime(),
                updated_by_id=request.user.id if hasattr(request, 'user') else 1
            )
            
            return json_response(plan.to_dict())
        except Exception as e:
            return json_response(error=f'创建测试计划失败: {str(e)}')
    
    @auth('exec.task.do')
    def put(self, request, plan_id):
        """更新测试计划"""
        try:
            try:
                plan = TestPlan.objects.get(pk=plan_id)
            except TestPlan.DoesNotExist:
                return json_response(error=f'测试计划ID {plan_id} 不存在')
            
            data = json.loads(request.body)
            name = data.get('name')
            description = data.get('description')
            category = data.get('category')
            commands = data.get('commands')
            files = data.get('files')
            steps = data.get('steps')
            step_interval = data.get('step_interval')
            file_path_strict = data.get('file_path_strict')
            variables = data.get('variables')
            
            if not name:
                return json_response(error='测试计划名称不能为空')
            
            # 更新测试计划
            if name is not None:
                plan.name = name
            if description is not None:
                plan.description = description
            if category is not None:
                plan.category = category
            if commands is not None:
                plan.commands = json.dumps(commands)
            if files is not None:
                plan.files = json.dumps(files)
            if steps is not None:
                plan.steps = json.dumps(steps)
            if step_interval is not None:
                plan.step_interval = step_interval
            if file_path_strict is not None:
                plan.file_path_strict = file_path_strict
            if variables is not None:
                plan.variables = json.dumps(variables)
            
            plan.updated_at = human_datetime()
            plan.updated_by_id = request.user.id if hasattr(request, 'user') else 1
            plan.save()
            
            return json_response(plan.to_dict())
        except Exception as e:
            return json_response(error=f'更新测试计划失败: {str(e)}')
    
    @auth('exec.task.do')
    def delete(self, request, plan_id=None):
        """删除测试计划"""
        try:
            if not plan_id:
                data = json.loads(request.body)
                plan_id = data.get('id')
                
            if not plan_id:
                return json_response(error='测试计划ID不能为空')
                
            try:
                plan = TestPlan.objects.get(pk=plan_id)
            except TestPlan.DoesNotExist:
                return json_response(error=f'测试计划ID {plan_id} 不存在')
            
            # 删除测试计划
            plan.delete()
            
            return json_response()
        except Exception as e:
            return json_response(error=f'删除测试计划失败: {str(e)}')


class TestCaseSetView(View):
    """测试用例集API"""
    
    @auth('exec.task.do')
    def get(self, request, case_set_id=None):
        """获取测试用例集列表或单个测试用例集"""
        try:
            if case_set_id:
                # 获取单个测试用例集
                try:
                    case_set = TestCaseSet.objects.get(pk=case_set_id)
                    return json_response(case_set.to_dict())
                except TestCaseSet.DoesNotExist:
                    return json_response(error=f'测试用例集ID {case_set_id} 不存在')
            
            # 获取测试用例集列表
            case_sets = TestCaseSet.objects.all().order_by('-id')
            return json_response([cs.to_dict() for cs in case_sets])
        except Exception as e:
            return json_response(error=f'获取测试用例集失败: {str(e)}')
    
    @auth('exec.task.do')
    def post(self, request):
        """创建新测试用例集"""
        try:
            data = json.loads(request.body)
            name = data.get('name')
            description = data.get('description')
            category = data.get('category')
            test_cases = data.get('test_cases', [])
            template_references = data.get('template_references', [])

            if not name:
                return json_response(error='测试用例集名称不能为空')

            # 分离自定义用例和模板引用
            custom_cases = []
            template_refs = []

            for case in test_cases:
                if case.get('is_template_reference'):
                    # 这是模板引用，添加到引用列表
                    template_id = case.get('template_id')
                    if template_id and template_id not in template_refs:
                        template_refs.append(template_id)
                else:
                    # 这是自定义用例
                    custom_cases.append(case)

            # 合并传入的template_references
            for ref_id in template_references:
                if ref_id not in template_refs:
                    template_refs.append(ref_id)

            # 创建新测试用例集
            case_set = TestCaseSet.objects.create(
                name=name,
                description=description,
                category=category,
                test_cases=json.dumps(custom_cases),
                template_references=json.dumps(template_refs),
                created_at=human_datetime(),
                created_by_id=request.user.id if hasattr(request, 'user') else 1,
                updated_at=human_datetime(),
                updated_by_id=request.user.id if hasattr(request, 'user') else 1
            )

            return json_response(case_set.to_dict())
        except Exception as e:
            return json_response(error=f'创建测试用例集失败: {str(e)}')
    
    @auth('exec.task.do')
    def put(self, request, case_set_id):
        """更新测试用例集"""
        try:
            try:
                case_set = TestCaseSet.objects.get(pk=case_set_id)
            except TestCaseSet.DoesNotExist:
                return json_response(error=f'测试用例集ID {case_set_id} 不存在')
            
            data = json.loads(request.body)
            name = data.get('name')
            description = data.get('description')
            category = data.get('category')
            test_cases = data.get('test_cases')
            template_references = data.get('template_references')

            if not name:
                return json_response(error='测试用例集名称不能为空')

            # 更新测试用例集
            if name is not None:
                case_set.name = name
            if description is not None:
                case_set.description = description
            if category is not None:
                case_set.category = category
            if test_cases is not None:
                # 分离自定义用例和模板引用
                custom_cases = []
                template_refs = []

                for case in test_cases:
                    if case.get('is_template_reference'):
                        # 这是模板引用，添加到引用列表
                        template_id = case.get('template_id')
                        if template_id and template_id not in template_refs:
                            template_refs.append(template_id)
                    else:
                        # 这是自定义用例
                        custom_cases.append(case)

                case_set.test_cases = json.dumps(custom_cases)
                case_set.template_references = json.dumps(template_refs)

            if template_references is not None:
                case_set.template_references = json.dumps(template_references)
            
            case_set.updated_at = human_datetime()
            case_set.updated_by_id = request.user.id if hasattr(request, 'user') else 1
            case_set.save()
            
            return json_response(case_set.to_dict())
        except Exception as e:
            return json_response(error=f'更新测试用例集失败: {str(e)}')
    
    @auth('exec.task.do')
    def delete(self, request, case_set_id=None):
        """删除测试用例集"""
        try:
            if not case_set_id:
                data = json.loads(request.body)
                case_set_id = data.get('id')
                
            if not case_set_id:
                return json_response(error='测试用例集ID不能为空')
                
            try:
                case_set = TestCaseSet.objects.get(pk=case_set_id)
            except TestCaseSet.DoesNotExist:
                return json_response(error=f'测试用例集ID {case_set_id} 不存在')
            
            # 删除测试用例集
            case_set.delete()
            
            return json_response()
        except Exception as e:
            return json_response(error=f'删除测试用例集失败: {str(e)}')


class TestCaseSetImportExportView(View):
    """测试用例集导入导出API"""
    
    @auth('exec.task.do')
    def get(self, request):
        """导出测试用例集"""
        try:
            case_set_id = request.GET.get('id')
            if not case_set_id:
                return json_response(error='测试用例集ID不能为空')
                
            try:
                case_set = TestCaseSet.objects.get(pk=case_set_id)
            except TestCaseSet.DoesNotExist:
                return json_response(error=f'测试用例集ID {case_set_id} 不存在')
            
            # 导出测试用例集
            export_data = case_set.to_dict()
            return json_response(export_data)
        except Exception as e:
            return json_response(error=f'导出测试用例集失败: {str(e)}')
    
    @auth('exec.task.do')
    def post(self, request):
        """导入测试用例集"""
        try:
            data = json.loads(request.body)
            import_data = data.get('import_data')
            
            if not import_data:
                return json_response(error='导入数据不能为空')
            
            name = import_data.get('name')
            description = import_data.get('description')
            category = import_data.get('category')
            test_cases = import_data.get('test_cases', [])
            
            if not name:
                return json_response(error='测试用例集名称不能为空')
            
            # 创建新测试用例集
            case_set = TestCaseSet.objects.create(
                name=name,
                description=description,
                category=category,
                test_cases=json.dumps(test_cases),
                created_at=human_datetime(),
                created_by_id=request.user.id if hasattr(request, 'user') else 1,
                updated_at=human_datetime(),
                updated_by_id=request.user.id if hasattr(request, 'user') else 1
            )
            
            return json_response(case_set.to_dict())
        except Exception as e:
            return json_response(error=f'导入测试用例集失败: {str(e)}')


class TestCaseTemplateView(View):
    """测试用例池API"""

    @auth('exec.task.do')
    def get(self, request, template_id=None):
        """获取用例模板列表或单个用例模板"""
        try:
            from .models import TestCaseTemplate

            if template_id:
                # 获取单个用例模板
                try:
                    template = TestCaseTemplate.objects.get(pk=template_id)
                    return json_response(template.to_dict())
                except TestCaseTemplate.DoesNotExist:
                    return json_response(error=f'用例模板ID {template_id} 不存在')

            # 获取用例模板列表
            templates = TestCaseTemplate.objects.all().order_by('sort_order', '-id')
            return json_response([t.to_dict() for t in templates])
        except Exception as e:
            return json_response(error=f'获取用例模板失败: {str(e)}')

    @auth('exec.task.do')
    def post(self, request):
        """创建新用例模板"""
        try:
            from .models import TestCaseTemplate

            data = json.loads(request.body)
            name = data.get('name')
            description = data.get('description')
            category = data.get('category')
            precondition = data.get('precondition')
            test_steps = data.get('test_steps')
            expected_result = data.get('expected_result')
            enabled = data.get('enabled', True)
            sort_order = data.get('sort_order', 0)

            if not name:
                return json_response(error='用例模板名称不能为空')

            # 创建新用例模板
            template = TestCaseTemplate.objects.create(
                name=name,
                description=description,
                category=category,
                precondition=precondition,
                test_steps=test_steps,
                expected_result=expected_result,
                enabled=enabled,
                sort_order=sort_order,
                created_at=human_datetime(),
                created_by_id=request.user.id if hasattr(request, 'user') else 1,
                updated_at=human_datetime(),
                updated_by_id=request.user.id if hasattr(request, 'user') else 1
            )

            return json_response(template.to_dict())
        except Exception as e:
            return json_response(error=f'创建用例模板失败: {str(e)}')

    @auth('exec.task.do')
    def put(self, request, template_id):
        """更新用例模板"""
        try:
            from .models import TestCaseTemplate

            try:
                template = TestCaseTemplate.objects.get(pk=template_id)
            except TestCaseTemplate.DoesNotExist:
                return json_response(error=f'用例模板ID {template_id} 不存在')

            data = json.loads(request.body)
            name = data.get('name')
            description = data.get('description')
            category = data.get('category')
            precondition = data.get('precondition')
            test_steps = data.get('test_steps')
            expected_result = data.get('expected_result')
            enabled = data.get('enabled')
            sort_order = data.get('sort_order')

            if not name:
                return json_response(error='用例模板名称不能为空')

            # 更新用例模板
            if name is not None:
                template.name = name
            if description is not None:
                template.description = description
            if category is not None:
                template.category = category
            if precondition is not None:
                template.precondition = precondition
            if test_steps is not None:
                template.test_steps = test_steps
            if expected_result is not None:
                template.expected_result = expected_result
            if enabled is not None:
                template.enabled = enabled
            if sort_order is not None:
                template.sort_order = sort_order

            template.updated_at = human_datetime()
            template.updated_by_id = request.user.id if hasattr(request, 'user') else 1
            template.save()

            return json_response(template.to_dict())
        except Exception as e:
            return json_response(error=f'更新用例模板失败: {str(e)}')

    @auth('exec.task.do')
    def delete(self, request, template_id=None):
        """删除用例模板"""
        try:
            from .models import TestCaseTemplate

            if not template_id:
                data = json.loads(request.body)
                template_id = data.get('id')

            if not template_id:
                return json_response(error='用例模板ID不能为空')

            try:
                template = TestCaseTemplate.objects.get(pk=template_id)
            except TestCaseTemplate.DoesNotExist:
                return json_response(error=f'用例模板ID {template_id} 不存在')

            # 删除用例模板
            template.delete()

            return json_response()
        except Exception as e:
            return json_response(error=f'删除用例模板失败: {str(e)}')


class TestCaseTemplateUsageView(View):
    """查询用例模板使用情况"""

    @auth('exec.task.do')
    def get(self, request, template_id):
        """获取使用了指定模板的用例集列表"""
        try:
            from .models import TestCaseSet
            import json

            # 查找引用了该模板的用例集
            case_sets = TestCaseSet.objects.all()
            referencing_sets = []

            for case_set in case_sets:
                try:
                    template_refs = json.loads(case_set.template_references) if case_set.template_references else []
                    if int(template_id) in template_refs:
                        referencing_sets.append({
                            'id': case_set.id,
                            'name': case_set.name,
                            'description': case_set.description,
                            'category': case_set.category,
                            'created_at': case_set.created_at
                        })
                except (json.JSONDecodeError, ValueError):
                    continue

            return json_response({
                'template_id': template_id,
                'referencing_sets': referencing_sets,
                'count': len(referencing_sets)
            })
        except Exception as e:
            return json_response(error=f'查询模板使用情况失败: {str(e)}')


class TestPlanImportExportView(View):
    """测试计划导入导出API"""
    
    @auth('exec.task.do')
    def get(self, request):
        """导出测试计划"""
        try:
            plan_id = request.GET.get('id')
            if not plan_id:
                return json_response(error='测试计划ID不能为空')
                
            try:
                plan = TestPlan.objects.get(pk=plan_id)
            except TestPlan.DoesNotExist:
                return json_response(error=f'测试计划ID {plan_id} 不存在')
            
            # 导出测试计划
            export_data = plan.to_dict()
            return json_response(export_data)
        except Exception as e:
            return json_response(error=f'导出测试计划失败: {str(e)}')
    
    @auth('exec.task.do')
    def post(self, request):
        """导入测试计划"""
        try:
            data = json.loads(request.body)
            import_data = data.get('import_data')
            
            if not import_data:
                return json_response(error='导入数据不能为空')
            
            name = import_data.get('name')
            description = import_data.get('description')
            category = import_data.get('category')
            commands = import_data.get('commands', [])
            files = import_data.get('files', [])
            steps = import_data.get('steps', [])
            step_interval = import_data.get('step_interval', 0)
            file_path_strict = import_data.get('file_path_strict', False)
            variables = import_data.get('variables', [])
            
            if not name:
                return json_response(error='测试计划名称不能为空')
            
            # 创建新测试计划
            plan = TestPlan.objects.create(
                name=name,
                description=description,
                category=category,
                commands=json.dumps(commands),
                files=json.dumps(files),
                steps=json.dumps(steps),
                step_interval=step_interval,
                file_path_strict=file_path_strict,
                variables=json.dumps(variables),
                created_at=human_datetime(),
                created_by_id=request.user.id if hasattr(request, 'user') else 1,
                updated_at=human_datetime(),
                updated_by_id=request.user.id if hasattr(request, 'user') else 1
            )
            
            return json_response(plan.to_dict())
        except Exception as e:
            return json_response(error=f'导入测试计划失败: {str(e)}')


class TestPlanExecutionView(View):
    """测试计划执行API"""
    
    @auth('exec.task.do')
    def get(self, request, execution_id=None):
        """获取测试计划执行记录列表或单个执行记录"""
        try:
            from .models import TestPlanExecution, TestPlanStepResult
            
            if execution_id:
                # 获取单个执行记录
                try:
                    execution = TestPlanExecution.objects.get(id=execution_id) if execution_id.isdigit() else TestPlanExecution.objects.get(token=execution_id)
                    
                    # 获取步骤结果
                    step_results = TestPlanStepResult.objects.filter(execution_id=execution.id).order_by('id')
                    step_results_data = [{
                        'id': sr.id,
                        'step_type': sr.step_type,
                        'step_name': sr.step_name,
                        'command': sr.command,
                        'file_path': sr.file_path,
                        'start_time': sr.start_time,
                        'end_time': sr.end_time,
                        'exit_code': sr.exit_code,
                        'output': sr.output,
                        'status': sr.status,
                        'assertion_enabled': sr.assertion_enabled,
                        'assertion_type': sr.assertion_type,
                        'assertion_config': json.loads(sr.assertion_config) if sr.assertion_config else {},
                        'assertion_status': sr.assertion_status
                    } for sr in step_results]
                    
                    # 构建响应数据
                    execution_data = execution.to_dict()
                    execution_data['step_results'] = step_results_data
                    
                    return json_response(execution_data)
                except (TestPlanExecution.DoesNotExist, ValueError):
                    return json_response(error=f'执行记录 {execution_id} 不存在')
            
            # 获取执行记录列表
            plan_id = request.GET.get('plan_id')
            status = request.GET.get('status')
            limit = int(request.GET.get('limit', 10))
            offset = int(request.GET.get('offset', 0))
            
            query = TestPlanExecution.objects.all()
            
            if plan_id:
                query = query.filter(plan_id=plan_id)
            if status:
                query = query.filter(status=status)
            
            count = query.count()
            records = query.order_by('-id')[offset:offset + limit]
            
            return json_response({
                'count': count,
                'data': [r.to_dict() for r in records]
            })
        except Exception as e:
            return json_response(error=f'获取测试计划执行记录失败: {str(e)}')
    
    @auth('exec.task.do')
    def post(self, request):
        """创建新的测试计划执行记录"""
        try:
            from .models import TestPlanExecution
            
            data = json.loads(request.body)
            plan_id = data.get('plan_id')
            host_ids = data.get('host_ids', [])
            
            if not plan_id:
                return json_response(error='测试计划ID不能为空')
            if not host_ids:
                return json_response(error='目标主机不能为空')
            
            # 获取测试计划
            try:
                plan = TestPlan.objects.get(pk=plan_id)
            except TestPlan.DoesNotExist:
                return json_response(error=f'测试计划ID {plan_id} 不存在')
            
            # 创建执行记录
            token = uuid.uuid4().hex
            execution = TestPlanExecution.objects.create(
                test_plan=plan,
                plan_id=plan.id,
                plan_name=plan.name,
                token=token,
                host_ids=json.dumps(host_ids),
                executor_id=request.user.id if hasattr(request, 'user') else 1,
                executor_name=request.user.username if hasattr(request, 'user') and hasattr(request.user, 'username') else '系统管理员',
                status='pending',
                start_time=human_datetime(),
                total_steps=len(json.loads(plan.steps)) if plan.steps else 0
            )
            
            # 这里可以添加异步任务来执行测试计划
            # 例如: execute_test_plan.delay(execution.id)
            
            return json_response(execution.to_dict())
        except Exception as e:
            return json_response(error=f'创建测试计划执行记录失败: {str(e)}')
    
    @auth('exec.task.do')
    def put(self, request, execution_id):
        """更新测试计划执行记录状态"""
        try:
            from .models import TestPlanExecution
            
            try:
                execution = TestPlanExecution.objects.get(id=execution_id) if execution_id.isdigit() else TestPlanExecution.objects.get(token=execution_id)
            except (TestPlanExecution.DoesNotExist, ValueError):
                return json_response(error=f'执行记录 {execution_id} 不存在')
            
            data = json.loads(request.body)
            status = data.get('status')
            completed_steps = data.get('completed_steps')
            end_time = data.get('end_time')
            
            # 更新执行记录
            if status is not None:
                execution.status = status
            if completed_steps is not None:
                execution.completed_steps = completed_steps
            if end_time is not None:
                execution.end_time = end_time
            elif status == 'completed' or status == 'failed':
                execution.end_time = human_datetime()
            
            execution.save()
            
            return json_response(execution.to_dict())
        except Exception as e:
            return json_response(error=f'更新测试计划执行记录失败: {str(e)}')
    
    @auth('exec.task.do')
    def delete(self, request, execution_id=None):
        """删除测试计划执行记录"""
        try:
            from .models import TestPlanExecution
            
            if not execution_id:
                data = json.loads(request.body)
                execution_id = data.get('id')
                
            if not execution_id:
                return json_response(error='执行记录ID不能为空')
                
            try:
                execution = TestPlanExecution.objects.get(id=execution_id) if execution_id.isdigit() else TestPlanExecution.objects.get(token=execution_id)
            except (TestPlanExecution.DoesNotExist, ValueError):
                return json_response(error=f'执行记录 {execution_id} 不存在')
            
            # 删除执行记录
            execution.delete()
            
            return json_response()
        except Exception as e:
            return json_response(error=f'删除测试计划执行记录失败: {str(e)}')


class TestPlanExecutionHistoryView(View):
    """测试计划执行历史记录API"""
    
    @auth('exec.task.do')
    def get(self, request, plan_id=None):
        """获取测试计划执行历史记录"""
        try:
            from .models import TestPlanExecution
            
            if plan_id:
                # 获取指定测试计划的执行历史
                executions = TestPlanExecution.objects.filter(plan_id=plan_id).order_by('-id')
                return json_response([e.to_dict() for e in executions])
            
            # 获取所有执行历史记录，支持分页和筛选
            limit = int(request.GET.get('limit', 10))
            offset = int(request.GET.get('offset', 0))
            plan_id = request.GET.get('plan_id')
            status = request.GET.get('status')
            
            query = TestPlanExecution.objects.all()
            
            if plan_id:
                query = query.filter(plan_id=plan_id)
            if status:
                query = query.filter(status=status)
            
            count = query.count()
            records = query.order_by('-id')[offset:offset + limit]
            
            return json_response({
                'count': count,
                'data': [r.to_dict() for r in records]
            })
        except Exception as e:
            return json_response(error=f'获取测试计划执行历史记录失败: {str(e)}')


class SampleDataView(View):
    """示例数据管理API - 仅用于开发和测试"""
    
    @auth('exec.task.do')
    def post(self, request):
        """创建示例数据"""
        try:
            data = json.loads(request.body)
            data_type = data.get('type', 'test_plan')
            
            if data_type == 'test_plan':
                # 创建示例测试计划
                plan = TestPlan.objects.create(
                    name='示例测试计划',
                    description='这是一个自动生成的示例测试计划',
                    category='示例',
                    commands=json.dumps([
                        {'name': '示例命令1', 'command': 'echo "Hello World"'},
                        {'name': '示例命令2', 'command': 'ls -la'}
                    ]),
                    files=json.dumps([]),
                    steps=json.dumps([
                        {'type': 'command', 'name': '执行示例命令1', 'command': 'echo "Hello World"'},
                        {'type': 'command', 'name': '执行示例命令2', 'command': 'ls -la'}
                    ]),
                    step_interval=1,
                    file_path_strict=False,
                    variables=json.dumps([]),
                    created_at=human_datetime(),
                    created_by_id=request.user.id if hasattr(request, 'user') else 1,
                    updated_at=human_datetime(),
                    updated_by_id=request.user.id if hasattr(request, 'user') else 1
                )
                return json_response({'message': '示例测试计划已创建', 'id': plan.id})
            elif data_type == 'test_case_set':
                # 创建示例测试用例集
                case_set = TestCaseSet.objects.create(
                    name='示例测试用例集',
                    description='这是一个自动生成的示例测试用例集',
                    category='示例',
                    test_cases=json.dumps([
                        {'name': '示例用例1', 'description': '这是示例用例1', 'expected_result': '预期结果1'},
                        {'name': '示例用例2', 'description': '这是示例用例2', 'expected_result': '预期结果2'}
                    ]),
                    created_at=human_datetime(),
                    created_by_id=request.user.id if hasattr(request, 'user') else 1,
                    updated_at=human_datetime(),
                    updated_by_id=request.user.id if hasattr(request, 'user') else 1
                )
                return json_response({'message': '示例测试用例集已创建', 'id': case_set.id})
            else:
                return json_response(error=f'不支持的数据类型: {data_type}')
        except Exception as e:
            return json_response(error=f'创建示例数据失败: {str(e)}')


class SSHConnectionTestView(View):
    """SSH连接测试API"""
    
    @auth('exec.task.do')
    def post(self, request):
        """测试SSH连接"""
        try:
            data = json.loads(request.body)
            host_id = data.get('host_id')
            
            if not host_id:
                return json_response(error='主机ID不能为空')
            
            # 获取主机信息
            try:
                from apps.host.models import Host
                host = Host.objects.get(pk=host_id)
            except Host.DoesNotExist:
                return json_response(error=f'主机ID {host_id} 不存在')
            
            # 测试SSH连接
            try:
                from libs.ssh import SSH
                ssh = SSH(host.hostname, host.port, host.username, host.private_key)
                ssh.ping()
                return json_response({'message': 'SSH连接成功'})
            except Exception as e:
                return json_response(error=f'SSH连接失败: {str(e)}')
        except Exception as e:
            return json_response(error=f'测试SSH连接失败: {str(e)}')


class TestResultTemplateView(View):
    """测试结果提取模板API"""
    
    @auth('exec.task.do')
    def get(self, request, template_id=None):
        """获取模板列表或单个模板"""
        try:
            from .models import TestResultTemplate
            
            if template_id:
                # 获取单个模板
                try:
                    template = TestResultTemplate.objects.get(pk=template_id)
                    return json_response({
                        'id': template.id,
                        'name': template.name,
                        'description': template.description,
                        'pattern': template.pattern,
                        'example_input': template.example_input,
                        'example_output': template.example_output,
                        'created_at': template.created_at,
                        'updated_at': template.updated_at
                    })
                except TestResultTemplate.DoesNotExist:
                    return json_response(error=f'模板ID {template_id} 不存在')
            
            # 获取模板列表
            templates = TestResultTemplate.objects.all().order_by('-id')
            return json_response([{
                'id': t.id,
                'name': t.name,
                'description': t.description,
                'pattern': t.pattern,
                'created_at': t.created_at,
                'updated_at': t.updated_at
            } for t in templates])
        except Exception as e:
            return json_response(error=f'获取测试结果提取模板失败: {str(e)}')
    
    @auth('exec.task.do')
    def post(self, request):
        """创建新模板"""
        try:
            from .models import TestResultTemplate
            
            data = json.loads(request.body)
            name = data.get('name')
            description = data.get('description')
            pattern = data.get('pattern')
            example_input = data.get('example_input')
            example_output = data.get('example_output')
            
            if not name:
                return json_response(error='模板名称不能为空')
            if not pattern:
                return json_response(error='提取模式不能为空')
            
            # 创建新模板
            template = TestResultTemplate.objects.create(
                name=name,
                description=description,
                pattern=pattern,
                example_input=example_input,
                example_output=example_output,
                created_at=human_datetime(),
                updated_at=human_datetime()
            )
            
            return json_response({
                'id': template.id,
                'name': template.name,
                'description': template.description,
                'pattern': template.pattern,
                'example_input': template.example_input,
                'example_output': template.example_output,
                'created_at': template.created_at,
                'updated_at': template.updated_at
            })
        except Exception as e:
            return json_response(error=f'创建测试结果提取模板失败: {str(e)}')
    
    @auth('exec.task.do')
    def put(self, request, template_id):
        """更新模板"""
        try:
            from .models import TestResultTemplate
            
            try:
                template = TestResultTemplate.objects.get(pk=template_id)
            except TestResultTemplate.DoesNotExist:
                return json_response(error=f'模板ID {template_id} 不存在')
            
            data = json.loads(request.body)
            name = data.get('name')
            description = data.get('description')
            pattern = data.get('pattern')
            example_input = data.get('example_input')
            example_output = data.get('example_output')
            
            if not name:
                return json_response(error='模板名称不能为空')
            if not pattern:
                return json_response(error='提取模式不能为空')
            
            # 更新模板
            template.name = name
            template.description = description
            template.pattern = pattern
            template.example_input = example_input
            template.example_output = example_output
            template.updated_at = human_datetime()
            template.save()
            
            return json_response({
                'id': template.id,
                'name': template.name,
                'description': template.description,
                'pattern': template.pattern,
                'example_input': template.example_input,
                'example_output': template.example_output,
                'created_at': template.created_at,
                'updated_at': template.updated_at
            })
        except Exception as e:
            return json_response(error=f'更新测试结果提取模板失败: {str(e)}')
    
    @auth('exec.task.do')
    def delete(self, request, template_id):
        """删除模板"""
        try:
            from .models import TestResultTemplate
            
            try:
                template = TestResultTemplate.objects.get(pk=template_id)
            except TestResultTemplate.DoesNotExist:
                return json_response(error=f'模板ID {template_id} 不存在')
            
            # 删除模板
            template.delete()
            
            return json_response()
        except Exception as e:
            return json_response(error=f'删除测试结果提取模板失败: {str(e)}')


class RemoteLogFetchView(View):
    """
    远程日志获取视图
    通过SSH连接到远程服务器获取指定路径的日志文件
    """

    @auth('exec.task.do')
    def post(self, request):
        form, error = JsonParser(
            Argument('log_path', help='请输入日志文件路径'),
            Argument('plan_name', required=False, help='测试计划名称（可选）'),
            Argument('host_id', type=int, help='请选择目标主机'),
            Argument('use_docker', required=False, help='是否使用Docker容器'),
            Argument('container_name', required=False, help='Docker容器名称'),
            Argument('server_name', required=False, help='远程服务器名称（兼容旧版本）')
        ).parse(request.body)

        if error:
            return json_response(error=error)

        # 处理use_docker参数，确保它是布尔值
        use_docker = form.use_docker in [True, 'true', 'True', 1, '1'] if form.use_docker is not None else False

        # 如果使用Docker但没有提供容器名称，返回错误
        if use_docker and not form.container_name:
            return json_response(error='使用Docker模式时必须提供容器名称')

        try:
            print(f"[DEBUG] 日志提取参数: log_path={form.log_path}, host_id={form.host_id}, use_docker={use_docker}, container_name={form.container_name}")

            # 使用主机选择器获取日志内容
            log_content = self._fetch_remote_log_from_host(
                form.log_path,
                form.host_id,
                use_docker=use_docker,
                container_name=form.container_name
            )

            # 获取主机信息用于返回
            host = Host.objects.get(pk=form.host_id)

            return json_response({
                'success': True,
                'content': log_content,
                'plan_name': form.plan_name,
                'log_path': form.log_path,
                'use_docker': use_docker,
                'container_name': form.container_name,
                'host_info': {
                    'id': host.id,
                    'name': host.name,
                    'hostname': host.hostname
                }
            })

        except Exception as e:
            return json_response(error=f"获取远程日志失败: {str(e)}")


class RemoteLogFetchMultipleView(View):
    """多文件远程日志提取视图"""

    @auth('exec.task.do')
    def post(self, request):
        form, error = JsonParser(
            Argument('log_paths', type=list, help='请输入日志文件路径列表'),
            Argument('host_id', type=int, help='请选择目标主机'),
            Argument('use_docker', required=False, help='是否使用Docker容器'),
            Argument('container_name', required=False, help='Docker容器名称'),
        ).parse(request.body)

        if error:
            return json_response(error=error)

        # 处理use_docker参数，确保它是布尔值
        use_docker = form.use_docker in [True, 'true', 'True', 1, '1'] if form.use_docker is not None else False

        # 如果使用Docker但没有提供容器名称，返回错误
        if use_docker and not form.container_name:
            return json_response(error='使用Docker模式时必须提供容器名称')

        try:
            print(f"[DEBUG] 多文件提取参数: log_paths={form.log_paths}, host_id={form.host_id}, use_docker={use_docker}, container_name={form.container_name}")

            # 获取主机信息
            host = Host.objects.get(pk=form.host_id)

            results = []
            for log_path in form.log_paths:
                try:
                    log_content = self._fetch_remote_log_from_host(
                        log_path.strip(),
                        form.host_id,
                        use_docker=use_docker,
                        container_name=form.container_name
                    )
                    results.append({
                        'log_path': log_path.strip(),
                        'content': log_content,
                        'success': True
                    })
                except Exception as e:
                    results.append({
                        'log_path': log_path.strip(),
                        'content': '',
                        'success': False,
                        'error': str(e)
                    })

            response_data = {
                'success': True,
                'results': results,
                'use_docker': use_docker,
                'container_name': form.container_name,
                'host_info': {
                    'id': host.id,
                    'name': host.name,
                    'hostname': host.hostname
                }
            }

            print(f"[DEBUG] 多文件响应数据: {response_data}")

            return json_response(response_data)

        except Host.DoesNotExist:
            return json_response(error=f"主机不存在 (ID: {form.host_id})")
        except Exception as e:
            return json_response(error=f"获取远程日志失败: {str(e)}")

    def _fetch_remote_log_from_host(self, log_path, host_id, use_docker=False, container_name=None):
        """
        通过指定主机的SSH连接获取远程日志文件内容
        支持从Docker容器内获取文件
        """
        try:
            # 获取主机对象
            host = Host.objects.get(pk=host_id)

            # 使用主机的SSH连接
            with host.get_ssh() as ssh:
                if use_docker and container_name:
                    # 使用docker exec命令从容器内读取文件
                    command = f"docker exec {container_name} cat {log_path}"
                else:
                    # 直接从主机文件系统读取
                    command = f"cat {log_path}"

                exit_code, content = ssh.exec_command_raw(command)

                if exit_code != 0:
                    if use_docker:
                        # Docker相关的错误提示更详细
                        if "No such container" in content:
                            raise Exception(f"Docker容器 '{container_name}' 不存在")
                        elif "No such file or directory" in content:
                            raise Exception(f"容器内文件 '{log_path}' 不存在")
                        else:
                            raise Exception(f"从Docker容器读取文件失败 (退出码: {exit_code}): {content}")
                    else:
                        raise Exception(f"读取日志文件失败 (退出码: {exit_code}): {content}")

                if not content.strip():
                    raise Exception("日志文件为空或不存在")

                return content

        except Host.DoesNotExist:
            raise Exception(f"主机不存在 (ID: {host_id})")
        except Exception as e:
            raise Exception(f"SSH连接或文件读取失败: {str(e)}")

    def _fetch_remote_log(self, log_path, server_name='mcp1'):
        """
        通过SSH连接获取远程日志文件内容
        """
        import paramiko
        import io
        
        try:
            # 这里应该从配置或数据库中获取SSH连接信息
            # 暂时使用硬编码的连接信息进行演示
            ssh_config = self._get_ssh_config(server_name)
            
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 建立SSH连接
            ssh.connect(
                hostname=ssh_config['hostname'],
                port=ssh_config.get('port', 22),
                username=ssh_config['username'],
                password=ssh_config.get('password'),
                key_filename=ssh_config.get('key_filename'),
                timeout=30
            )
            
            # 执行cat命令读取日志文件
            command = f"cat {log_path}"
            stdin, stdout, stderr = ssh.exec_command(command)
            
            # 读取输出
            content = stdout.read().decode('utf-8', errors='ignore')
            error_output = stderr.read().decode('utf-8', errors='ignore')
            
            ssh.close()
            
            if error_output and not content:
                raise Exception(f"读取日志文件失败: {error_output}")
            
            return content
            
        except Exception as e:
            raise Exception(f"SSH连接或读取文件失败: {str(e)}")
    
    def _get_ssh_config(self, server_name):
        """
        获取SSH连接配置
        在实际应用中，这应该从数据库或配置文件中获取
        """
        # 尝试从Host模型中获取SSH配置
        try:
            from apps.host.models import Host
            host = Host.objects.filter(name=server_name).first()
            if host:
                return {
                    'hostname': host.hostname,
                    'username': host.username,
                    'password': host.password if hasattr(host, 'password') else None,
                    'key_filename': None,  # 可以根据需要配置SSH密钥
                    'port': host.port
                }
        except:
            pass
        
        # 如果没有找到Host记录，使用MCP服务器的配置
        # 这里根据实际的MCP服务器配置进行调整
        configs = {
            'mcp1': {
                'hostname': '127.0.0.1',  # 替换为实际的MCP服务器IP
                'username': 'admin',      # 替换为实际的用户名
                'password': 'admin',      # 替换为实际的密码
                'key_filename': None,     # 如果使用SSH密钥，指定密钥文件路径
                'port': 22
            }
        }
        
        if server_name not in configs:
            raise Exception(f"未找到服务器 {server_name} 的SSH配置")
        
        return configs[server_name]


class DockerContainerListView(View):
    """获取指定主机上的Docker容器列表"""

    @auth('exec.task.do')
    def get(self, request):
        host_id = request.GET.get('host_id')


        if not host_id:
            print('[DEBUG] DockerContainerListView: host_id参数缺失')
            return json_response(error='请指定主机ID')

        try:
            host_id = int(host_id)
            host = Host.objects.get(pk=host_id)


            # 使用SSH连接和Python Docker SDK获取容器列表
            with host.get_ssh() as ssh:
                # 创建远程Python脚本来获取Docker容器信息
                docker_script = '''
import json
import sys

try:
    import docker
    client = docker.from_env()
    containers = []

    for container in client.containers.list(all=False):  # all=False 只获取运行中的容器
        # 获取容器的详细信息
        container_info = {
            "name": container.name,
            "image": container.image.tags[0] if container.image.tags else container.image.id[:12],
            "status": container.status,
            "id": container.id[:12],
            "created": container.attrs["Created"],
            "state": container.attrs.get("State", {}).get("Status", "unknown")
        }
        containers.append(container_info)

    result = {"success": True, "containers": containers}
    print(json.dumps(result))

except ImportError as e:
    result = {"success": False, "error": "Docker SDK未安装，请在目标主机安装: pip install docker"}
    print(json.dumps(result))
    sys.exit(1)
except Exception as e:
    result = {"success": False, "error": str(e)}
    print(json.dumps(result))
    sys.exit(1)
'''

                print(f'[DEBUG] DockerContainerListView: 使用Python Docker SDK获取容器列表')

                # 执行Python脚本
                exit_code, output = ssh.exec_command_raw(f'python3 -c "{docker_script}"')

                print(f'[DEBUG] DockerContainerListView: 脚本执行结果，退出码 = {exit_code}')
                print(f'[DEBUG] DockerContainerListView: 脚本输出 = {output}')

                # 如果Python3不可用，尝试python
                if exit_code != 0:
                    print(f'[DEBUG] DockerContainerListView: python3失败，尝试python')
                    exit_code, output = ssh.exec_command_raw(f'python -c "{docker_script}"')
                    print(f'[DEBUG] DockerContainerListView: python执行结果，退出码 = {exit_code}')
                    print(f'[DEBUG] DockerContainerListView: python输出 = {output}')

                if exit_code != 0:
                    # 如果Python Docker SDK不可用，回退到docker ps命令
                    print(f'[DEBUG] DockerContainerListView: Python Docker SDK不可用，回退到docker ps命令')
                    fallback_result = self._fallback_docker_ps(ssh)
                    return json_response({
                        **fallback_result,
                        'host_info': {
                            'id': host.id,
                            'name': host.name,
                            'hostname': host.hostname
                        }
                    })

                try:
                    # 解析JSON输出
                    import json
                    result = json.loads(output.strip())
                    if not result.get('success'):
                        print(f'[DEBUG] DockerContainerListView: Docker SDK返回错误: {result.get("error")}')
                        # 回退到docker ps命令
                        fallback_result = self._fallback_docker_ps(ssh)
                        return json_response({
                            **fallback_result,
                            'host_info': {
                                'id': host.id,
                                'name': host.name,
                                'hostname': host.hostname
                            }
                        })

                    containers = result.get('containers', [])
                    print(f'[DEBUG] DockerContainerListView: 通过Docker SDK获取到 {len(containers)} 个容器')

                except json.JSONDecodeError as e:
                    print(f'[DEBUG] DockerContainerListView: JSON解析失败: {e}，回退到docker ps命令')
                    fallback_result = self._fallback_docker_ps(ssh)
                    return json_response({
                        **fallback_result,
                        'host_info': {
                            'id': host.id,
                            'name': host.name,
                            'hostname': host.hostname
                        }
                    })

                return json_response({
                    'success': True,
                    'containers': containers,
                    'host_info': {
                        'id': host.id,
                        'name': host.name,
                        'hostname': host.hostname
                    }
                })

        except Host.DoesNotExist:
            return json_response(error=f"主机不存在 (ID: {host_id})")
        except ValueError:
            return json_response(error='主机ID格式错误')
        except Exception as e:
            return json_response(error=f"获取容器列表失败: {str(e)}")

    def _fallback_docker_ps(self, ssh):
        """回退方法：使用docker ps命令获取容器列表"""
        print(f'[DEBUG] DockerContainerListView: 使用docker ps命令作为回退方案')

        # 执行docker ps命令获取运行中的容器
        command = "docker ps --format 'table {{.Names}}\t{{.Image}}\t{{.Status}}' --no-trunc"
        print(f'[DEBUG] DockerContainerListView: 执行命令 = {command}')
        exit_code, output = ssh.exec_command_raw(command)
        print(f'[DEBUG] DockerContainerListView: 命令执行结果，退出码 = {exit_code}')
        print(f'[DEBUG] DockerContainerListView: 命令输出 = {output}')

        if exit_code != 0:
            if "command not found" in output.lower():
                return json_response(error='目标主机未安装Docker')
            else:
                return json_response(error=f'获取Docker容器列表失败: {output}')

        # 解析输出
        containers = []
        lines = output.strip().split('\n')
        print(f'[DEBUG] DockerContainerListView: 输出行数 = {len(lines)}')

        # 跳过表头
        if len(lines) > 1:
            for i, line in enumerate(lines[1:], 1):
                if line.strip():
                    # 使用正则表达式分割多个空格
                    import re
                    parts = re.split(r'\s+', line.strip())
                    print(f'[DEBUG] DockerContainerListView: 第{i}行解析 = {parts}')
                    if len(parts) >= 3:
                        container = {
                            'name': parts[0].strip(),
                            'image': parts[1].strip(),
                            'status': ' '.join(parts[2:]).strip()  # 状态可能包含多个词
                        }
                        containers.append(container)
                        print(f'[DEBUG] DockerContainerListView: 添加容器 = {container}')

        print(f'[DEBUG] DockerContainerListView: 最终容器列表数量 = {len(containers)}')

        return {
            'success': True,
            'containers': containers
        }


class DockerFileSystemView(View):
    """Docker容器文件系统浏览"""

    def get(self, request):
        """获取Docker容器内的文件列表"""
        host_id = request.GET.get('host_id')
        container_name = request.GET.get('container_name')
        path = request.GET.get('path', '/')

        if not host_id:
            return json_response(error='请指定主机ID')

        if not container_name:
            return json_response(error='请指定容器名称')

        try:
            host_id = int(host_id)
            host = Host.objects.get(pk=host_id)

            # 使用SSH连接获取Docker容器文件列表
            with host.get_ssh() as ssh:
                # 验证容器是否存在
                check_cmd = f"docker ps --format '{{{{.Names}}}}' | grep -w {container_name}"
                exit_code, output = ssh.exec_command_raw(check_cmd)

                if exit_code != 0 or container_name not in output:
                    return json_response(error=f'容器 "{container_name}" 不存在或未运行')

                # 获取文件列表，使用更详细的ls命令来处理符号链接
                list_cmd = f"docker exec {container_name} ls -la '{path}' 2>/dev/null"
                exit_code, output = ssh.exec_command_raw(list_cmd)

                if exit_code != 0:
                    # 静默处理错误，返回空列表而不是错误信息
                    return json_response({
                        'success': True,
                        'items': [],
                        'current_path': path,
                        'host_info': {
                            'id': host.id,
                            'name': host.name,
                            'hostname': host.hostname
                        },
                        'container_name': container_name
                    })

                # 解析ls输出
                files = []
                directories = []

                lines = output.strip().split('\n')
                for line in lines:
                    if not line.strip() or line.startswith('total'):
                        continue

                    parts = line.split()
                    if len(parts) < 9:
                        continue

                    permissions = parts[0]
                    # 处理符号链接：name可能包含 " -> target" 格式
                    name_part = ' '.join(parts[8:])

                    # 分离符号链接的名称和目标
                    if ' -> ' in name_part:
                        name = name_part.split(' -> ')[0]
                        link_target = name_part.split(' -> ')[1]
                        is_symlink = True
                    else:
                        name = name_part
                        link_target = None
                        is_symlink = False

                    # 跳过当前目录和父目录
                    if name in ['.', '..']:
                        continue

                    # 判断类型：目录、符号链接或文件
                    is_directory = permissions.startswith('d')
                    is_link = permissions.startswith('l')
                    size = parts[4] if not is_directory else ''

                    item = {
                        'name': name,
                        'is_directory': is_directory or is_link,  # 符号链接也当作可进入的目录处理
                        'is_symlink': is_symlink,
                        'link_target': link_target,
                        'size': size,
                        'permissions': permissions,
                        'full_path': f"{path.rstrip('/')}/{name}"
                    }

                    if is_directory or is_link:
                        directories.append(item)
                    else:
                        files.append(item)

                # 目录排在前面，然后是文件
                all_items = sorted(directories, key=lambda x: x['name']) + sorted(files, key=lambda x: x['name'])

                return json_response({
                    'success': True,
                    'items': all_items,
                    'current_path': path,
                    'host_info': {
                        'id': host.id,
                        'name': host.name,
                        'hostname': host.hostname
                    },
                    'container_name': container_name
                })

        except Host.DoesNotExist:
            return json_response(error=f"主机不存在 (ID: {host_id})")
        except ValueError:
            return json_response(error='主机ID格式错误')
        except Exception as e:
            return json_response(error=f"获取文件列表失败: {str(e)}")


class DockerPathAutoCompleteView(View):
    """Docker容器路径自动补全"""

    def get(self, request):
        """获取路径自动补全建议"""
        host_id = request.GET.get('host_id')
        container_name = request.GET.get('container_name')
        partial_path = request.GET.get('path', '')

        if not host_id or not container_name:
            return json_response(error='缺少必要参数')

        try:
            host_id = int(host_id)
            host = Host.objects.get(pk=host_id)

            # 分析输入的路径
            if not partial_path or partial_path == '/':
                # 如果是根路径或空，返回根目录内容
                search_path = '/'
                prefix = ''
            else:
                # 分离目录和文件名前缀
                if partial_path.endswith('/'):
                    search_path = partial_path
                    prefix = ''
                else:
                    search_path = '/'.join(partial_path.split('/')[:-1]) or '/'
                    prefix = partial_path.split('/')[-1]

            with host.get_ssh() as ssh:
                # 验证容器是否存在
                check_cmd = f"docker ps --format '{{{{.Names}}}}' | grep -w {container_name}"
                exit_code, output = ssh.exec_command_raw(check_cmd)

                if exit_code != 0 or container_name not in output:
                    return json_response(error=f'容器 "{container_name}" 不存在或未运行')

                # 获取目录内容
                list_cmd = f"docker exec {container_name} ls -la '{search_path}' 2>/dev/null"
                exit_code, output = ssh.exec_command_raw(list_cmd)

                if exit_code != 0:
                    return json_response({'suggestions': []})

                suggestions = []
                lines = output.strip().split('\n')

                for line in lines:
                    if not line.strip() or line.startswith('total'):
                        continue

                    parts = line.split()
                    if len(parts) < 9:
                        continue

                    permissions = parts[0]
                    # 处理符号链接：name可能包含 " -> target" 格式
                    name_part = ' '.join(parts[8:])

                    # 分离符号链接的名称和目标
                    if ' -> ' in name_part:
                        name = name_part.split(' -> ')[0]
                    else:
                        name = name_part

                    # 跳过当前目录和父目录
                    if name in ['.', '..']:
                        continue

                    # 如果有前缀，只返回匹配的项
                    if prefix and not name.startswith(prefix):
                        continue

                    # 判断类型：目录、符号链接或文件
                    is_directory = permissions.startswith('d')
                    is_link = permissions.startswith('l')

                    # 构建完整路径
                    if search_path == '/':
                        full_path = f"/{name}"
                    else:
                        full_path = f"{search_path.rstrip('/')}/{name}"

                    # 如果是目录或符号链接，添加斜杠
                    if is_directory or is_link:
                        full_path += '/'

                    suggestions.append({
                        'path': full_path,
                        'name': name,
                        'is_directory': is_directory or is_link,
                        'type': 'directory' if (is_directory or is_link) else 'file'
                    })

                # 按名称排序，目录在前
                suggestions.sort(key=lambda x: (not x['is_directory'], x['name']))

                return json_response({
                    'suggestions': suggestions[:20],  # 限制返回数量
                    'search_path': search_path,
                    'prefix': prefix
                })

        except Host.DoesNotExist:
            return json_response(error=f"主机不存在 (ID: {host_id})")
        except ValueError:
            return json_response(error='主机ID格式错误')
        except Exception as e:
            return json_response(error=f"获取路径建议失败: {str(e)}")


class DockerFileContentView(View):
    """获取Docker容器文件内容"""

    def _analyze_log_with_ai(self, content, filename, parsed_data=None):
        """使用AI分析日志内容，基于已有解析结果提供深度洞察"""
        try:
            # 定义分析结果的JSON结构
            analysis_schema = {
                "type": "object",
                "properties": {
                    "performance_assessment": {
                        "type": "object",
                        "properties": {
                            "score": {"type": "number", "description": "性能评分(0-100)"},
                            "level": {"type": "string", "description": "性能等级(优秀/良好/一般/较差)"},
                            "summary": {"type": "string", "description": "性能评估摘要"}
                        }
                    },
                    "anomalies": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "type": {"type": "string", "description": "异常类型"},
                                "description": {"type": "string", "description": "异常描述"},
                                "severity": {"type": "string", "description": "严重程度(高/中/低)"},
                                "time_range": {"type": "string", "description": "发生时间范围"}
                            }
                        }
                    },
                    "recommendations": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "category": {"type": "string", "description": "建议类别"},
                                "description": {"type": "string", "description": "具体建议"},
                                "priority": {"type": "string", "description": "优先级(高/中/低)"},
                                "impact": {"type": "string", "description": "预期影响"}
                            }
                        }
                    },
                    "insights": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "深度洞察和发现"
                    }
                }
            }

            # 限制内容长度，避免token过多
            max_content_length = 4000
            truncated_content = content[:max_content_length]
            if len(content) > max_content_length:
                truncated_content += "\n... (内容已截断)"

            # 构建增强的提示词
            parsed_info = ""
            if parsed_data:
                parsed_info = f"""
已解析的结构化指标：
{json.dumps(parsed_data, ensure_ascii=False, indent=2)}
"""

            prompt = f"""请作为专业的系统运维专家，深度分析以下日志文件：

文件信息：
- 文件名: {filename}
- 文件大小: {len(content)} 字节
{parsed_info}
原始日志内容（前4000字符）：
{truncated_content}

请提供专业的分析报告，包括：

1. 性能评估：基于日志内容评估系统性能状况，给出0-100分的评分
2. 异常检测：识别错误、警告、超时、异常状态等问题模式
3. 优化建议：提供具体可行的性能优化和问题解决建议
4. 深度洞察：发现潜在的系统问题和改进机会

请确保分析结果专业、准确、可操作。"""

            # 使用AI助手进行分析
            from libs.ai_helper import AI
            result = AI.json(prompt, schema=analysis_schema)

            if not result:
                return None

            return result

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"AI日志分析失败: {str(e)}")
            return None

    def _parse_benchmark_log(self, content, filename):
        """解析基准测试日志文件，提取关键指标"""
        import re

        # 基准测试指标提取模板
        patterns = {
            'successful_requests': r'Successful requests:\s+([\d,]+)',
            'benchmark_duration': r'Benchmark duration \(s\):\s+([\d.]+)',
            'total_input_tokens': r'Total input tokens:\s+([\d,]+)',
            'total_generated_tokens': r'Total generated tokens:\s+([\d,]+)',
            'request_throughput': r'Request throughput \(req/s\):\s+([\d.]+)',
            'output_token_throughput': r'Output token throughput \(tok/s\):\s+([\d.]+)',
            'total_token_throughput': r'Total Token throughput \(tok/s\):\s+([\d.]+)',
            'mean_ttft': r'Mean TTFT \(ms\):\s+([\d.]+)',
            'median_ttft': r'Median TTFT \(ms\):\s+([\d.]+)',
            'p99_ttft': r'P99 TTFT \(ms\):\s+([\d.]+)',
            'mean_tpot': r'Mean TPOT \(ms\):\s+([\d.]+)',
            'median_tpot': r'Median TPOT \(ms\):\s+([\d.]+)',
            'p99_tpot': r'P99 TPOT \(ms\):\s+([\d.]+)',
            'mean_itl': r'Mean ITL \(ms\):\s+([\d.]+)',
            'median_itl': r'Median ITL \(ms\):\s+([\d.]+)',
            'p99_itl': r'P99 ITL \(ms\):\s+([\d.]+)'
        }

        result = {'filename': filename}

        for key, pattern in patterns.items():
            match = re.search(pattern, content)
            if match:
                value = match.group(1)
                # 移除数字中的逗号并转换为数字
                try:
                    numeric_value = float(value.replace(',', ''))
                    result[key] = numeric_value
                except ValueError:
                    result[key] = 0
            else:
                result[key] = 0

        return result

    def get(self, request):
        """获取指定文件的内容"""
        host_id = request.GET.get('host_id')
        container_name = request.GET.get('container_name')
        file_path = request.GET.get('file_path')

        if not all([host_id, container_name, file_path]):
            return json_response(error='缺少必要参数')

        try:
            host_id = int(host_id)
            host = Host.objects.get(id=host_id)

            with host.get_ssh() as ssh:
                # 验证容器是否存在
                check_cmd = f"docker ps --format '{{{{.Names}}}}' | grep -w {container_name}"
                exit_code, output = ssh.exec_command_raw(check_cmd)

                if exit_code != 0 or container_name not in output:
                    return json_response(error=f'容器 "{container_name}" 不存在或未运行')

                # 获取文件内容，限制大小避免内存问题
                cat_cmd = f"docker exec {container_name} head -c 1048576 '{file_path}' 2>/dev/null"
                exit_code, content = ssh.exec_command_raw(cat_cmd)

                if exit_code != 0:
                    return json_response({
                        'success': False,
                        'content': '',
                        'file_path': file_path,
                        'error': '无法读取文件内容'
                    })

                # 智能解析文件内容
                filename = file_path.split('/')[-1]  # 提取文件名
                parsed_data = None
                ai_analysis = None

                # 检查是否是基准测试日志文件
                keywords = ['benchmark', 'successful requests', 'throughput']
                content_lower = content.lower()
                found_keywords = [kw for kw in keywords if kw in content_lower]

                if found_keywords:
                    try:
                        parsed_data = self._parse_benchmark_log(content, filename)
                    except Exception as e:
                        # 解析失败时，parsed_data保持为None
                        pass

                # 检查是否启用AI分析
                use_ai = request.GET.get('use_ai', 'false').lower() == 'true'
                if use_ai:
                    ai_analysis = self._analyze_log_with_ai(content, filename, parsed_data)

                return json_response({
                    'success': True,
                    'file_path': file_path,
                    'size': len(content.encode('utf-8')),
                    'parsed_data': parsed_data,  # 解析后的结构化数据
                    'ai_analysis': ai_analysis,  # AI分析结果
                    'host_info': {
                        'id': host.id,
                        'name': host.name,
                        'hostname': host.hostname
                    },
                    'container_name': container_name
                })

        except Host.DoesNotExist:
            return json_response(error=f"主机不存在 (ID: {host_id})")
        except ValueError:
            return json_response(error='主机ID格式错误')
        except Exception as e:
            return json_response(error=f"获取文件内容失败: {str(e)}")


class DockerPathSuggestionView(View):
    """Docker容器路径建议API"""

    @auth('exec.transfer.do')
    def get(self, request):
        """获取Docker容器内路径建议"""
        host_id = request.GET.get('host_id')
        container_name = request.GET.get('container_name')
        path_prefix = request.GET.get('path_prefix', '/')

        if not host_id or not container_name:
            return json_response(error='请指定主机ID和容器名称')

        try:
            host = Host.objects.get(pk=host_id)

            # 权限检查
            if not has_host_perm(request.user, host.id):
                return json_response(error='无权访问该主机')

            with host.get_ssh() as ssh:
                # 验证容器是否存在且运行中
                exit_code, output = ssh.exec_command_raw(f'docker inspect --format="{{{{.State.Running}}}}" {container_name}')
                if exit_code != 0:
                    return json_response(error=f'容器 {container_name} 不存在或无法访问')

                if output.strip().lower() != 'true':
                    return json_response(error=f'容器 {container_name} 未运行')

                # 获取路径建议
                # 安全性检查：防止路径注入
                if any(char in path_prefix for char in [';', '&', '|', '`', '$', '(', ')']):
                    return json_response(error='路径包含非法字符')

                # 如果路径以/结尾，列出目录内容；否则进行模糊匹配
                if path_prefix.endswith('/'):
                    # 列出目录内容，限制输出行数防止过多数据
                    escaped_path = path_prefix.replace('"', '\\"')
                    command = f'docker exec {container_name} sh -c "ls -la \\"{escaped_path}\\" 2>/dev/null | head -50" || echo "DIRECTORY_NOT_FOUND"'
                else:
                    # 模糊匹配，限制搜索深度和结果数量
                    parent_dir = '/'.join(path_prefix.split('/')[:-1]) or '/'
                    filename_prefix = path_prefix.split('/')[-1]
                    escaped_parent = parent_dir.replace('"', '\\"')
                    escaped_prefix = filename_prefix.replace('"', '\\"')
                    command = f'docker exec {container_name} sh -c "find \\"{escaped_parent}\\" -maxdepth 1 -name \\"{escaped_prefix}*\\" 2>/dev/null | head -20" || echo "NO_MATCHES"'

                exit_code, output = ssh.exec_command_raw(command)

                if exit_code != 0 or 'DIRECTORY_NOT_FOUND' in output or 'NO_MATCHES' in output:
                    return json_response({
                        'success': True,
                        'suggestions': [],
                        'message': '未找到匹配的路径'
                    })

                # 解析输出
                suggestions = []
                lines = output.strip().split('\n')

                for line in lines:
                    if line.strip() and not line.startswith('total'):
                        # 解析ls -la输出或find输出
                        if path_prefix.endswith('/'):
                            # ls -la格式
                            parts = line.split()
                            if len(parts) >= 9:
                                filename = ' '.join(parts[8:])
                                if filename not in ['.', '..']:
                                    is_dir = line.startswith('d')
                                    suggestions.append({
                                        'name': filename,
                                        'path': f"{path_prefix}{filename}",
                                        'is_directory': is_dir,
                                        'type': 'directory' if is_dir else 'file'
                                    })
                        else:
                            # find输出
                            filepath = line.strip()
                            if filepath:
                                # 检查是否为目录
                                check_cmd = f'docker exec {container_name} test -d "{filepath}" && echo "DIR" || echo "FILE"'
                                _, type_output = ssh.exec_command_raw(check_cmd)
                                is_dir = type_output.strip() == 'DIR'

                                suggestions.append({
                                    'name': filepath.split('/')[-1],
                                    'path': filepath,
                                    'is_directory': is_dir,
                                    'type': 'directory' if is_dir else 'file'
                                })

                return json_response({
                    'success': True,
                    'suggestions': suggestions[:20],  # 限制返回数量
                    'container_name': container_name,
                    'path_prefix': path_prefix
                })

        except Host.DoesNotExist:
            return json_response(error='主机不存在')
        except Exception as e:
            return json_response(error=f'获取路径建议失败: {str(e)}')


class TransferTemplateView(View):
    """文件分发配置模板管理"""

    @auth('exec.transfer.do')
    def get(self, request):
        """获取配置模板列表"""
        # 获取用户自己的模板和公开模板
        templates = []

        # 用户自己的模板
        user_templates = request.user.transfertemplate_set.all()
        for template in user_templates:
            tmp = template.to_view()
            tmp['is_owner'] = True
            templates.append(tmp)

        # 公开模板（排除用户自己的）
        from apps.exec.models import TransferTemplate
        public_templates = TransferTemplate.objects.filter(
            is_public=True
        ).exclude(user=request.user)

        for template in public_templates:
            tmp = template.to_view()
            tmp['is_owner'] = False
            tmp['owner_name'] = template.user.nickname or template.user.username
            templates.append(tmp)

        return json_response(templates)

    @auth('exec.transfer.do')
    def post(self, request):
        """创建配置模板"""
        form, error = JsonParser(
            Argument('name', help='请输入模板名称'),
            Argument('description', default='', required=False),
            Argument('config', type=dict, help='请提供配置信息'),
            Argument('is_public', type=bool, default=False, required=False)
        ).parse(request.body)

        if error:
            return json_response(error=error)

        # 检查模板名称是否重复
        from apps.exec.models import TransferTemplate
        if TransferTemplate.objects.filter(user=request.user, name=form.name).exists():
            return json_response(error='模板名称已存在')

        # 创建模板
        template = TransferTemplate.objects.create(
            user=request.user,
            name=form.name,
            description=form.description,
            config=json.dumps(form.config),
            is_public=form.is_public
        )

        return json_response(template.to_view())

    @auth('exec.transfer.do')
    def patch(self, request, template_id):
        """更新配置模板"""
        form, error = JsonParser(
            Argument('name', required=False),
            Argument('description', required=False),
            Argument('config', type=dict, required=False),
            Argument('is_public', type=bool, required=False)
        ).parse(request.body)

        if error:
            return json_response(error=error)

        try:
            from apps.exec.models import TransferTemplate
            template = TransferTemplate.objects.get(pk=template_id, user=request.user)

            if form.name is not None:
                # 检查名称是否重复
                if TransferTemplate.objects.filter(
                    user=request.user,
                    name=form.name
                ).exclude(pk=template_id).exists():
                    return json_response(error='模板名称已存在')
                template.name = form.name

            if form.description is not None:
                template.description = form.description

            if form.config is not None:
                template.config = json.dumps(form.config)

            if form.is_public is not None:
                template.is_public = form.is_public

            template.updated_at = human_datetime()
            template.save()

            return json_response(template.to_view())

        except TransferTemplate.DoesNotExist:
            return json_response(error='模板不存在或无权访问')

    @auth('exec.transfer.do')
    def delete(self, request, template_id):
        """删除配置模板"""
        try:
            from apps.exec.models import TransferTemplate
            template = TransferTemplate.objects.get(pk=template_id, user=request.user)
            template.delete()
            return json_response()
        except TransferTemplate.DoesNotExist:
            return json_response(error='模板不存在或无权访问')


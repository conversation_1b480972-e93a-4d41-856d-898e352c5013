#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据存储健壮性测试脚本
测试PerformanceTestData模型的数据验证和存储功能
"""

import os
import sys
import django
import json
from decimal import Decimal

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth.models import User
from apps.model_storage.models import PerformanceTestData
from apps.account.models import User as SpugUser


class DataRobustnessTest:
    """数据存储健壮性测试类"""
    
    def __init__(self):
        self.client = Client()
        self.setup_test_user()
    
    def setup_test_user(self):
        """设置测试用户"""
        try:
            self.user = SpugUser.objects.get(username='admin')
        except SpugUser.DoesNotExist:
            self.user = SpugUser.objects.create_user(
                username='test_admin',
                password='admin123',
                nickname='测试管理员'
            )

        # 简化登录，不使用force_login避免数据库问题
        print(f"✅ 测试用户设置完成: {self.user.username}")
    
    def test_valid_data_creation(self):
        """测试有效数据创建"""
        print("🧪 测试有效数据创建...")

        # 直接使用模型创建，避免API权限问题
        try:
            record = PerformanceTestData.objects.create(
                model_name='test_model',
                filename='test_file.log',
                success_requests=100,
                benchmark_duration=10.5,
                input_tokens=1000,
                output_tokens=500,
                request_throughput=9.52,
                output_token_throughput=47.6,
                total_token_throughput=142.8,
                avg_ttft=105.2,
                median_ttft=98.7,
                p99_ttft=234.5,
                avg_tpot=21.3,
                median_tpot=19.8,
                p99_tpot=45.6
            )
            print("✅ 有效数据创建成功")
            return record
        except Exception as e:
            print(f"❌ 有效数据创建失败: {e}")
            return None
    
    def test_invalid_data_validation(self):
        """测试无效数据验证"""
        print("\n🧪 测试数据验证逻辑...")

        # 测试负数值处理
        try:
            record = PerformanceTestData.objects.create(
                model_name='test_model',
                filename='negative_test.log',
                success_requests=-10,  # 负数
                benchmark_duration=-5.0,  # 负数
                input_tokens=-100  # 负数
            )
            # 检查是否被转换为0或正数
            if record.success_requests >= 0 and record.benchmark_duration >= 0 and record.input_tokens >= 0:
                print("✅ 负数值处理正确 - 已转换为非负数")
            else:
                print("❌ 负数值处理失败 - 仍然是负数")
            record.delete()
        except Exception as e:
            print(f"⚠️ 负数值测试异常: {e}")

        # 测试超长文件名
        try:
            long_filename = 'a' * 300  # 超过256字符限制
            record = PerformanceTestData.objects.create(
                model_name='test_model',
                filename=long_filename,
                success_requests=100
            )
            if len(record.filename) <= 256:
                print("✅ 超长文件名处理正确 - 已截断或拒绝")
            else:
                print("❌ 超长文件名处理失败 - 未截断")
            record.delete()
        except Exception as e:
            print(f"✅ 超长文件名正确拒绝: {str(e)[:100]}...")

        # 测试空文件名
        try:
            record = PerformanceTestData.objects.create(
                model_name='test_model',
                filename='',  # 空文件名
                success_requests=100
            )
            print("⚠️ 空文件名被接受 - 可能需要前端验证")
            record.delete()
        except Exception as e:
            print(f"✅ 空文件名正确拒绝: {str(e)[:100]}...")
    
    def test_batch_operations(self):
        """测试批量操作"""
        print("\n🧪 测试批量数据创建...")

        # 直接使用模型批量创建
        try:
            records = []
            for i in range(5):
                record = PerformanceTestData.objects.create(
                    model_name='batch_test_model',
                    filename=f'batch_test_{i}.log',
                    success_requests=100 + i,
                    benchmark_duration=10.0 + i,
                    input_tokens=1000 + i * 100,
                    output_tokens=500 + i * 50
                )
                records.append(record)

            print(f"✅ 批量创建成功: {len(records)} 条记录")
            return records
        except Exception as e:
            print(f"❌ 批量创建失败: {e}")
            return []
    
    def test_duplicate_prevention(self):
        """测试重复数据防护"""
        print("\n🧪 测试重复数据防护...")

        try:
            # 创建第一条记录
            record1 = PerformanceTestData.objects.create(
                model_name='duplicate_test',
                filename='duplicate_test.log',
                success_requests=100
            )
            print("✅ 第一条记录创建成功")

            # 尝试创建重复记录
            try:
                record2 = PerformanceTestData.objects.create(
                    model_name='duplicate_test',
                    filename='duplicate_test.log',  # 相同文件名
                    success_requests=200
                )
                print("⚠️ 重复数据被允许 - 可能需要唯一性约束")
                record2.delete()
            except Exception as e:
                print(f"✅ 重复数据防护成功: {str(e)[:100]}...")

            record1.delete()
        except Exception as e:
            print(f"❌ 重复数据测试失败: {e}")
    
    def test_data_type_conversion(self):
        """测试数据类型转换"""
        print("\n🧪 测试数据类型转换...")
        
        # 创建一条记录用于测试to_dict方法
        record = PerformanceTestData.objects.create(
            model_name='type_test',
            filename='type_test.log',
            success_requests=100,
            benchmark_duration=10.5,
            input_tokens=1000,
            output_tokens=500
        )
        
        # 测试to_dict方法
        dict_data = record.to_dict()
        
        # 验证数据类型
        type_checks = [
            ('id', int),
            ('success_requests', int),
            ('benchmark_duration', float),
            ('input_tokens', int),
            ('output_tokens', int),
            ('model_name', str),
            ('filename', str)
        ]
        
        all_passed = True
        for field, expected_type in type_checks:
            if not isinstance(dict_data[field], expected_type):
                print(f"❌ 字段 {field} 类型错误: 期望 {expected_type}, 实际 {type(dict_data[field])}")
                all_passed = False
        
        if all_passed:
            print("✅ 数据类型转换测试通过")
        
        # 清理测试数据
        record.delete()
    
    def cleanup_test_data(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据...")
        
        test_models = [
            'test_model',
            'batch_test_model', 
            'duplicate_test',
            'type_test'
        ]
        
        for model_name in test_models:
            deleted_count = PerformanceTestData.objects.filter(
                model_name=model_name
            ).delete()[0]
            if deleted_count > 0:
                print(f"🗑️ 清理 {model_name}: {deleted_count} 条记录")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始数据存储健壮性测试\n")
        
        try:
            # 运行各项测试
            self.test_valid_data_creation()
            self.test_invalid_data_validation()
            self.test_batch_operations()
            self.test_duplicate_prevention()
            self.test_data_type_conversion()
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
        
        finally:
            # 清理测试数据
            self.cleanup_test_data()
            print("\n✅ 数据存储健壮性测试完成")


if __name__ == '__main__':
    tester = DataRobustnessTest()
    tester.run_all_tests()

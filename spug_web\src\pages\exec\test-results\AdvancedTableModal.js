import React, { useState, useMemo } from 'react';
import { Modal, Input, Button, Space, Tag, Tooltip } from 'antd';
import { SearchOutlined, DownloadOutlined, SortAscendingOutlined, SortDescendingOutlined } from '@ant-design/icons';
import { useReactTable, getCoreRowModel, getSortedRowModel, getFilteredRowModel, flexRender, createColumnHelper } from '@tanstack/react-table';
import * as XLSX from 'xlsx';
import styles from './AdvancedTableModal.module.less';

const { Search } = Input;

const AdvancedTableModal = ({ visible, onCancel, data = [] }) => {
  const [globalFilter, setGlobalFilter] = useState('');
  const [sorting, setSorting] = useState([]);

  // 创建列定义
  const columnHelper = createColumnHelper();
  
  const columns = useMemo(() => [
    columnHelper.accessor('id', {
      header: 'ID',
      size: 80,
      cell: info => info.getValue(),
    }),
    columnHelper.accessor('plan_name', {
      header: '测试计划',
      size: 200,
      cell: info => (
        <Tooltip title={info.getValue()}>
          <div className={styles.cellContent}>{info.getValue()}</div>
        </Tooltip>
      ),
    }),
    columnHelper.accessor('model_name', {
      header: '模型名称',
      size: 150,
      cell: info => (
        <Tag color="blue">{info.getValue()}</Tag>
      ),
    }),
    columnHelper.accessor('gpu_model', {
      header: 'GPU型号',
      size: 120,
      cell: info => (
        <Tag color="green">{info.getValue()}</Tag>
      ),
    }),
    columnHelper.accessor('status', {
      header: '状态',
      size: 100,
      cell: info => {
        const status = info.getValue();
        const color = status === 'success' ? 'green' : status === 'failed' ? 'red' : 'orange';
        return <Tag color={color}>{status}</Tag>;
      },
    }),
    columnHelper.accessor('created_at', {
      header: '创建时间',
      size: 180,
      cell: info => info.getValue(),
    }),
    columnHelper.accessor('duration', {
      header: '执行时长',
      size: 120,
      cell: info => `${info.getValue()}s`,
    }),
  ], [columnHelper]);

  // 创建表格实例
  const table = useReactTable({
    data,
    columns,
    state: {
      globalFilter,
      sorting,
    },
    onGlobalFilterChange: setGlobalFilter,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    globalFilterFn: 'includesString',
  });

  // 导出Excel功能
  const handleExport = () => {
    const exportData = table.getFilteredRowModel().rows.map(row => {
      const rowData = {};
      row.getVisibleCells().forEach(cell => {
        const columnId = cell.column.id;
        const header = cell.column.columnDef.header;
        rowData[header] = cell.getValue();
      });
      return rowData;
    });

    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '测试结果');
    XLSX.writeFile(wb, `测试结果_${new Date().toISOString().split('T')[0]}.xlsx`);
  };

  return (
    <Modal
      title="高级表格分析"
      open={visible}
      onCancel={onCancel}
      width="90%"
      style={{ top: 20 }}
      footer={[
        <Button key="export" icon={<DownloadOutlined />} onClick={handleExport}>
          导出Excel
        </Button>,
        <Button key="close" onClick={onCancel}>
          关闭
        </Button>,
      ]}
    >
      <div className={styles.container}>
        {/* 工具栏 */}
        <div className={styles.toolbar}>
          <Space>
            <Search
              placeholder="搜索所有列..."
              value={globalFilter ?? ''}
              onChange={(e) => setGlobalFilter(e.target.value)}
              style={{ width: 300 }}
              allowClear
            />
            <span className={styles.info}>
              显示 {table.getFilteredRowModel().rows.length} / {data.length} 条记录
            </span>
          </Space>
        </div>

        {/* 表格 */}
        <div className={styles.tableContainer}>
          <table className={styles.table}>
            <thead>
              {table.getHeaderGroups().map(headerGroup => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <th
                      key={header.id}
                      style={{ width: header.getSize() }}
                      className={styles.th}
                    >
                      {header.isPlaceholder ? null : (
                        <div
                          className={`${styles.headerContent} ${
                            header.column.getCanSort() ? styles.sortable : ''
                          }`}
                          onClick={header.column.getToggleSortingHandler()}
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          {header.column.getCanSort() && (
                            <span className={styles.sortIcon}>
                              {{
                                asc: <SortAscendingOutlined />,
                                desc: <SortDescendingOutlined />,
                              }[header.column.getIsSorted()] ?? <SortAscendingOutlined style={{ opacity: 0.3 }} />}
                            </span>
                          )}
                        </div>
                      )}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody>
              {table.getRowModel().rows.map(row => (
                <tr key={row.id} className={styles.tr}>
                  {row.getVisibleCells().map(cell => (
                    <td key={cell.id} className={styles.td}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </Modal>
  );
};

export default AdvancedTableModal;

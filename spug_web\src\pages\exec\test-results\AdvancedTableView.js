import React, { useState, useMemo } from 'react';
import { 
  Table, 
  Input, 
  Button, 
  Space, 
  Tag, 
  Progress, 
  Tooltip,
  Row,
  Col,
  Card,
  Statistic
} from 'antd';
import { 
  SearchOutlined, 
  DownloadOutlined, 
  SortAscendingOutlined,
  SortDescendingOutlined,
  FilterOutlined
} from '@ant-design/icons';
import { 
  useReactTable, 
  getCoreRowModel, 
  getSortedRowModel, 
  getFilteredRowModel, 
  flexRender, 
  createColumnHelper 
} from '@tanstack/react-table';
import * as XLSX from 'xlsx';
import styles from './AdvancedTableView.module.less';

const { Search } = Input;

function AdvancedTableView({ data, loading, rowSelection }) {
  const [globalFilter, setGlobalFilter] = useState('');
  const [sorting, setSorting] = useState([]);

  // 统计数据
  const stats = useMemo(() => {
    if (!data || data.length === 0) return {};
    
    const total = data.length;
    const completed = data.filter(item => item.status === 'completed').length;
    const running = data.filter(item => item.status === 'running').length;
    const failed = data.filter(item => item.status === 'failed').length;
    const avgMetrics = data.reduce((sum, item) => sum + (item.total_metrics || 0), 0) / total;
    
    return {
      total,
      completed,
      running,
      failed,
      avgMetrics: Math.round(avgMetrics * 100) / 100
    };
  }, [data]);

  // 定义列
  const columns = useMemo(() => [
    {
      accessorKey: 'id',
      header: 'ID',
      size: 80,
      cell: ({ getValue }) => (
        <span className={styles.idCell}>{getValue()}</span>
      )
    },
    {
      accessorKey: 'plan_name',
      header: '测试计划',
      size: 150,
      cell: ({ getValue }) => (
        <Tooltip title={getValue()}>
          <span className={styles.planName}>{getValue()}</span>
        </Tooltip>
      )
    },
    {
      accessorKey: 'model_name',
      header: '模型名称',
      size: 120,
      cell: ({ getValue }) => (
        <Tag color="blue">{getValue() || '未指定'}</Tag>
      )
    },
    {
      accessorKey: 'gpu_name',
      header: 'GPU型号',
      size: 120,
      cell: ({ getValue }) => (
        <Tag color="green">{getValue() || '未指定'}</Tag>
      )
    },
    {
      accessorKey: 'status',
      header: '状态',
      size: 100,
      cell: ({ getValue }) => {
        const status = getValue();
        const statusConfig = {
          'completed': { color: 'success', text: '已完成' },
          'running': { color: 'processing', text: '运行中' },
          'failed': { color: 'error', text: '失败' },
          'pending': { color: 'default', text: '等待中' }
        };
        const config = statusConfig[status] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      accessorKey: 'total_metrics',
      header: '总指标数',
      size: 100,
      cell: ({ getValue }) => (
        <span className={styles.metricsCount}>{getValue() || 0}</span>
      )
    },
    {
      accessorKey: 'confirmed_metrics',
      header: '已确认指标',
      size: 120,
      cell: ({ getValue, row }) => {
        const confirmed = getValue() || 0;
        const total = row.original.total_metrics || 0;
        const percentage = total > 0 ? Math.round((confirmed / total) * 100) : 0;
        
        return (
          <div className={styles.metricsProgress}>
            <div>{confirmed}/{total}</div>
            <Progress 
              percent={percentage} 
              size="small" 
              strokeColor={percentage >= 80 ? '#52c41a' : percentage >= 60 ? '#faad14' : '#ff4d4f'}
            />
          </div>
        );
      }
    },
    {
      accessorKey: 'created_time',
      header: '创建时间',
      size: 160,
      cell: ({ getValue }) => (
        <span className={styles.timeCell}>
          {getValue() ? new Date(getValue()).toLocaleString() : '-'}
        </span>
      )
    },
    {
      accessorKey: 'duration',
      header: '执行时长',
      size: 100,
      cell: ({ getValue }) => {
        const duration = getValue();
        if (!duration) return '-';
        
        const seconds = Math.floor(duration / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
          return `${hours}h ${minutes % 60}m`;
        } else if (minutes > 0) {
          return `${minutes}m ${seconds % 60}s`;
        } else {
          return `${seconds}s`;
        }
      }
    }
  ], []);

  // 创建表格实例
  const table = useReactTable({
    data: data || [],
    columns,
    state: {
      globalFilter,
      sorting,
    },
    onGlobalFilterChange: setGlobalFilter,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    globalFilterFn: 'includesString',
  });

  // 导出Excel
  const handleExport = () => {
    const exportData = table.getFilteredRowModel().rows.map(row => {
      const rowData = {};
      columns.forEach(column => {
        const value = row.getValue(column.accessorKey);
        rowData[column.header] = value;
      });
      return rowData;
    });

    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '测试结果');
    XLSX.writeFile(wb, `测试结果_${new Date().toISOString().split('T')[0]}.xlsx`);
  };

  return (
    <div className={styles.advancedTableContainer}>
      {/* 统计卡片 */}
      <Row gutter={16} className={styles.statsRow}>
        <Col span={6}>
          <Card size="small">
            <Statistic title="总数" value={stats.total} />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic 
              title="已完成" 
              value={stats.completed} 
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic 
              title="运行中" 
              value={stats.running} 
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic 
              title="失败" 
              value={stats.failed} 
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 工具栏 */}
      <div className={styles.toolbar}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Search
                placeholder="搜索测试结果..."
                value={globalFilter}
                onChange={(e) => setGlobalFilter(e.target.value)}
                style={{ width: 300 }}
                allowClear
              />
              <Button icon={<FilterOutlined />}>
                高级筛选
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button 
                icon={<DownloadOutlined />} 
                onClick={handleExport}
                disabled={!data || data.length === 0}
              >
                导出Excel
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* 表格 */}
      <div className={styles.tableWrapper}>
        <table className={styles.advancedTable}>
          <thead>
            {table.getHeaderGroups().map(headerGroup => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <th 
                    key={header.id}
                    style={{ width: header.getSize() }}
                    className={styles.tableHeader}
                  >
                    <div
                      className={`${styles.headerContent} ${
                        header.column.getCanSort() ? styles.sortable : ''
                      }`}
                      onClick={header.column.getToggleSortingHandler()}
                    >
                      {flexRender(header.column.columnDef.header, header.getContext())}
                      {header.column.getIsSorted() && (
                        <span className={styles.sortIcon}>
                          {header.column.getIsSorted() === 'desc' ? 
                            <SortDescendingOutlined /> : 
                            <SortAscendingOutlined />
                          }
                        </span>
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map(row => (
              <tr key={row.id} className={styles.tableRow}>
                {row.getVisibleCells().map(cell => (
                  <td key={cell.id} className={styles.tableCell}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 分页信息 */}
      <div className={styles.pagination}>
        <span>
          显示 {table.getFilteredRowModel().rows.length} 条结果
          {globalFilter && ` (从 ${data?.length || 0} 条中筛选)`}
        </span>
      </div>
    </div>
  );
}

export default AdvancedTableView;
